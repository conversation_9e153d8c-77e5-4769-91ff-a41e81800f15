import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RbacService } from './rbac.service';
import { UserRole, Permission } from './roles.enum';
import { User } from '../../entities/user.entity';

// Metadata keys for decorators
export const ROLES_KEY = 'roles';
export const PERMISSIONS_KEY = 'permissions';
export const REQUIRE_ALL_PERMISSIONS_KEY = 'requireAllPermissions';
export const RESOURCE_KEY = 'resource';

@Injectable()
export class RbacGuard implements CanActivate {
  private readonly logger = new Logger(RbacGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly rbacService: RbacService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user: User = request.user;

    if (!user) {
      this.logger.warn('RBAC Guard: No user found in request');
      throw new ForbiddenException('Authentication required');
    }

    // Get metadata from decorators
    const requiredRoles = this.reflector.getAllAndOverride<UserRole[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    const requiredPermissions = this.reflector.getAllAndOverride<Permission[]>(PERMISSIONS_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    const requireAllPermissions = this.reflector.getAllAndOverride<boolean>(
      REQUIRE_ALL_PERMISSIONS_KEY,
      [context.getHandler(), context.getClass()],
    );

    const resourceInfo = this.reflector.getAllAndOverride<any>(RESOURCE_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // If no roles or permissions are required, allow access
    if (!requiredRoles && !requiredPermissions) {
      this.logger.debug('RBAC Guard: No role or permission requirements, allowing access', {
        userId: user.id,
        userRole: user.role,
      });
      return true;
    }

    try {
      // Check roles if specified
      if (requiredRoles && requiredRoles.length > 0) {
        const hasRole = await this.rbacService.userHasAnyRole(user.id, requiredRoles);
        
        if (!hasRole) {
          this.logger.warn('RBAC Guard: Role check failed', {
            userId: user.id,
            userRole: user.role,
            requiredRoles,
          });
          throw new ForbiddenException('Insufficient role privileges');
        }

        this.logger.debug('RBAC Guard: Role check passed', {
          userId: user.id,
          userRole: user.role,
          requiredRoles,
        });
      }

      // Check permissions if specified
      if (requiredPermissions && requiredPermissions.length > 0) {
        let hasPermission: boolean;

        if (requireAllPermissions) {
          hasPermission = await this.rbacService.userHasAllPermissions(user.id, requiredPermissions);
        } else {
          hasPermission = await this.rbacService.userHasAnyPermission(user.id, requiredPermissions);
        }

        if (!hasPermission) {
          this.logger.warn('RBAC Guard: Permission check failed', {
            userId: user.id,
            userRole: user.role,
            requiredPermissions,
            requireAllPermissions,
          });
          throw new ForbiddenException('Insufficient permissions');
        }

        this.logger.debug('RBAC Guard: Permission check passed', {
          userId: user.id,
          userRole: user.role,
          requiredPermissions,
          requireAllPermissions,
        });
      }

      // Check resource access if specified
      if (resourceInfo) {
        const resourceAccess = await this.checkResourceAccess(user, request, resourceInfo);
        
        if (!resourceAccess) {
          this.logger.warn('RBAC Guard: Resource access check failed', {
            userId: user.id,
            userRole: user.role,
            resourceInfo,
          });
          throw new ForbiddenException('Access denied to this resource');
        }

        this.logger.debug('RBAC Guard: Resource access check passed', {
          userId: user.id,
          userRole: user.role,
          resourceInfo,
        });
      }

      this.logger.debug('RBAC Guard: All checks passed, granting access', {
        userId: user.id,
        userRole: user.role,
        requiredRoles,
        requiredPermissions,
        resourceInfo,
      });

      return true;
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }

      this.logger.error('RBAC Guard: Unexpected error during authorization', {
        error: error.message,
        userId: user.id,
        userRole: user.role,
      });

      throw new ForbiddenException('Authorization check failed');
    }
  }

  private async checkResourceAccess(
    user: User,
    request: any,
    resourceInfo: any,
  ): Promise<boolean> {
    try {
      const { type, idParam, permission } = resourceInfo;

      // Extract resource ID from request parameters
      const resourceId = request.params[idParam];

      if (!resourceId) {
        this.logger.warn('RBAC Guard: Resource ID not found in request parameters', {
          userId: user.id,
          idParam,
          params: request.params,
        });
        return false;
      }

      // For admin users, allow access to all resources
      if (user.role === UserRole.ADMIN) {
        this.logger.debug('RBAC Guard: Admin user, granting resource access', {
          userId: user.id,
          resourceType: type,
          resourceId,
        });
        return true;
      }

      // Check if user has the required permission for this resource
      if (permission) {
        const hasAccess = await this.rbacService.checkAccess(user.id, permission, {
          userId: user.id,
          resourceId,
          resourceType: type,
        });

        return hasAccess;
      }

      // If no specific permission is required, check resource ownership
      const ownsResource = await this.rbacService.checkResourceOwnership({
        userId: user.id,
        resourceId,
        resourceType: type,
      });

      return ownsResource;
    } catch (error) {
      this.logger.error('RBAC Guard: Error checking resource access', {
        error: error.message,
        userId: user.id,
        resourceInfo,
      });
      return false;
    }
  }
}
