# ArabLMS - Educational Platform MVP

A streamlined educational platform that enables instructors to create branded online schools and deliver courses to students, with a focus on the Middle Eastern market and Arabic language support.

## 🚀 Features

- **For Instructors**: Simple school creation with custom branding and course delivery
- **For Students**: Easy course discovery, purchase, and consumption
- **For Platform**: Sustainable revenue through transaction fees

## 🏗️ Architecture

This is a monorepo containing:

- **Frontend**: Next.js 15+ with App Router, TypeScript, Tailwind CSS, and Shadcn UI
- **Backend**: NestJS with TypeScript, PostgreSQL, and RESTful APIs

## 📋 Prerequisites

- Node.js 18+ 
- npm or yarn
- PostgreSQL database
- Git

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd arablms
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.project.example .env
   # Edit .env with your configuration
   ```

4. **Install workspace dependencies**
   ```bash
   # Install frontend dependencies
   cd frontend && npm install && cd ..
   
   # Install backend dependencies  
   cd backend && npm install && cd ..
   ```

## 🚀 Development

### Start both frontend and backend
```bash
npm run dev
```

### Start individually
```bash
# Frontend only (runs on http://localhost:3000)
npm run dev:frontend

# Backend only (runs on http://localhost:3001)
npm run dev:backend
```

### Other commands
```bash
# Build both projects
npm run build

# Start production servers
npm run start

# Lint all code
npm run lint

# Format all code
npm run format
```

## 📁 Project Structure

```
arablms/
├── frontend/          # Next.js frontend application
│   ├── src/
│   │   ├── app/       # App Router pages
│   │   ├── components/# Reusable components
│   │   └── lib/       # Utilities and configurations
│   ├── public/        # Static assets
│   └── package.json
├── backend/           # NestJS backend application
│   ├── src/
│   │   ├── modules/   # Feature modules
│   │   ├── common/    # Shared utilities
│   │   └── main.ts    # Application entry point
│   └── package.json
├── scripts/           # Project scripts and PRD
├── tasks/             # TaskMaster AI task management
└── package.json       # Root workspace configuration
```

## 🌐 API Documentation

The backend API runs on `http://localhost:3001/api` and includes:

- **Health Check**: `GET /api/health`
- **Authentication**: `/api/auth/*`
- **Users**: `/api/users/*`
- **Schools**: `/api/schools/*`
- **Courses**: `/api/courses/*`

## 🔧 Technology Stack

### Frontend
- **Framework**: Next.js 15+ with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Shadcn UI
- **State Management**: React Context/Zustand
- **Forms**: React Hook Form + Zod

### Backend
- **Framework**: NestJS
- **Language**: TypeScript
- **Database**: PostgreSQL with TypeORM
- **Authentication**: JWT with Passport
- **Validation**: Class Validator
- **Documentation**: Swagger/OpenAPI

### External Services
- **Database**: Supabase (PostgreSQL)
- **File Storage**: Supabase Storage
- **Video Hosting**: Bunny.net
- **Payments**: Stripe
- **Email**: SMTP (configurable)

## 🌍 Internationalization

- Primary language: Arabic (RTL support)
- Secondary language: English
- Currency: EGP (Egyptian Pound)
- Regional payment methods supported

## 🧪 Testing

```bash
# Run frontend tests
cd frontend && npm test

# Run backend tests  
cd backend && npm test

# Run e2e tests
npm run test:e2e
```

## 📦 Deployment

### Production Build
```bash
npm run build
```

### Environment Variables
Ensure all required environment variables are set in production:
- Database credentials
- JWT secrets
- External service API keys
- CORS origins

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions, please contact the development team or create an issue in the repository.
