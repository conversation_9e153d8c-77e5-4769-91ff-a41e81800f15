import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, map } from 'rxjs/operators';
import { JwtService } from '../jwt.service';

@Injectable()
export class AuthInterceptor implements NestInterceptor {
  private readonly logger = new Logger(AuthInterceptor.name);

  constructor(private readonly jwtService: JwtService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    const startTime = Date.now();

    // Log request details
    this.logger.debug('Request intercepted', {
      method: request.method,
      path: request.path,
      userId: request.user?.id,
      userAgent: request.headers['user-agent'],
      ip: request.ip,
    });

    return next.handle().pipe(
      map((data) => {
        // Add security headers
        response.setHeader('X-Content-Type-Options', 'nosniff');
        response.setHeader('X-Frame-Options', 'DENY');
        response.setHeader('X-XSS-Protection', '1; mode=block');
        response.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

        // Remove sensitive data from responses if present
        if (data && typeof data === 'object') {
          return this.sanitizeResponse(data);
        }

        return data;
      }),
      tap({
        next: (data) => {
          const duration = Date.now() - startTime;
          
          this.logger.debug('Request completed successfully', {
            method: request.method,
            path: request.path,
            statusCode: response.statusCode,
            duration: `${duration}ms`,
            userId: request.user?.id,
            responseSize: JSON.stringify(data).length,
          });

          // Log authentication events for audit
          if (request.user) {
            this.jwtService.logAuthEvent('REQUEST_COMPLETED', request.user.id, {
              method: request.method,
              path: request.path,
              statusCode: response.statusCode,
              duration,
            });
          }
        },
        error: (error) => {
          const duration = Date.now() - startTime;
          
          this.logger.error('Request failed', {
            method: request.method,
            path: request.path,
            error: error.message,
            statusCode: error.status || 500,
            duration: `${duration}ms`,
            userId: request.user?.id,
          });

          // Log failed requests for security monitoring
          if (request.user) {
            this.jwtService.logAuthEvent('REQUEST_FAILED', request.user.id, {
              method: request.method,
              path: request.path,
              error: error.message,
              statusCode: error.status || 500,
              duration,
            });
          }
        },
      }),
    );
  }

  private sanitizeResponse(data: any): any {
    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeResponse(item));
    }

    if (data && typeof data === 'object') {
      const sanitized = { ...data };

      // Remove sensitive fields
      const sensitiveFields = [
        'password',
        'password_hash',
        'passwordHash',
        'secret',
        'token',
        'refreshToken',
        'privateKey',
        'apiKey',
      ];

      sensitiveFields.forEach(field => {
        if (field in sanitized) {
          delete sanitized[field];
        }
      });

      // Recursively sanitize nested objects
      Object.keys(sanitized).forEach(key => {
        if (sanitized[key] && typeof sanitized[key] === 'object') {
          sanitized[key] = this.sanitizeResponse(sanitized[key]);
        }
      });

      return sanitized;
    }

    return data;
  }
}

@Injectable()
export class TokenRefreshInterceptor implements NestInterceptor {
  private readonly logger = new Logger(TokenRefreshInterceptor.name);

  constructor(private readonly jwtService: JwtService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();

    return next.handle().pipe(
      tap(() => {
        // Check if token is close to expiration and add refresh hint
        if (request.user && request.token) {
          this.checkTokenExpiration(request, response);
        }
      }),
    );
  }

  private async checkTokenExpiration(request: any, response: any) {
    try {
      const payload = await this.jwtService.verifyAndDecode(request.token);
      
      if (payload) {
        const currentTime = Math.floor(Date.now() / 1000);
        const timeUntilExpiry = payload.exp - currentTime;
        
        // If token expires in less than 5 minutes, add refresh hint
        if (timeUntilExpiry < 300) {
          response.setHeader('X-Token-Refresh-Hint', 'true');
          response.setHeader('X-Token-Expires-In', timeUntilExpiry.toString());
          
          this.logger.debug('Token refresh hint added', {
            userId: request.user.id,
            expiresIn: timeUntilExpiry,
          });
        }
      }
    } catch (error) {
      this.logger.warn('Failed to check token expiration', {
        error: error.message,
        userId: request.user?.id,
      });
    }
  }
}
