# ArabLMS Database Schema Documentation

## Overview

This document describes the database schema implementation for the ArabLMS platform. The schema is designed to support a multi-tenant educational platform where instructors can create schools, courses, and manage student enrollments.

## Database Technology

- **Database**: PostgreSQL 14+ (Supabase)
- **ORM**: TypeORM 0.3.x + Supabase Client
- **Migration System**: Supabase Migrations
- **Security**: Row-Level Security (RLS) policies
- **Authentication**: Supabase Auth
- **Real-time**: Supabase Real-time subscriptions

## Schema Design

### Core Entities

#### 1. Users (`users`)
Stores user account information for students, instructors, and administrators.

**Key Fields:**
- `id` (UUID, Primary Key)
- `email` (Unique, Required)
- `password_hash` (Required)
- `role` (Enum: student, instructor, admin)
- `profile_data` (JSONB) - Flexible profile information

**Relationships:**
- One-to-Many with Schools (as owner)
- One-to-Many with Enrollments
- One-to-Many with Payments

#### 2. Schools (`schools`)
Represents educational institutions or branded learning environments.

**Key Fields:**
- `id` (UUID, Primary Key)
- `name` (Required)
- `subdomain` (Unique, Required)
- `owner_id` (Foreign Key to Users)
- `settings` (JSONB) - School configuration

**Relationships:**
- Many-to-One with Users (owner)
- One-to-Many with Courses

#### 3. Courses (`courses`)
Individual courses offered by schools.

**Key Fields:**
- `id` (UUID, Primary Key)
- `title` (Required)
- `price` (Decimal)
- `school_id` (Foreign Key to Schools)
- `status` (Enum: draft, published, archived)
- `level` (Enum: beginner, intermediate, advanced)
- `metadata` (JSONB) - Course configuration

**Relationships:**
- Many-to-One with Schools
- One-to-Many with Modules
- One-to-Many with Enrollments
- One-to-Many with Payments

#### 4. Modules (`modules`)
Course sections that group related lessons.

**Key Fields:**
- `id` (UUID, Primary Key)
- `title` (Required)
- `course_id` (Foreign Key to Courses)
- `order` (Integer) - Display order
- `metadata` (JSONB) - Module configuration

**Relationships:**
- Many-to-One with Courses
- One-to-Many with Lessons

#### 5. Lessons (`lessons`)
Individual learning units within modules.

**Key Fields:**
- `id` (UUID, Primary Key)
- `title` (Required)
- `module_id` (Foreign Key to Modules)
- `content_type` (Enum: video, text, quiz, assignment, document, live_session)
- `content` (JSONB) - Lesson content and configuration
- `order` (Integer) - Display order

**Relationships:**
- Many-to-One with Modules

#### 6. Enrollments (`enrollments`)
Tracks student enrollment in courses and progress.

**Key Fields:**
- `id` (UUID, Primary Key)
- `user_id` (Foreign Key to Users)
- `course_id` (Foreign Key to Courses)
- `status` (Enum: active, completed, suspended, cancelled)
- `progress` (JSONB) - Learning progress tracking

**Relationships:**
- Many-to-One with Users
- Many-to-One with Courses

**Constraints:**
- Unique constraint on (user_id, course_id)

#### 7. Payments (`payments`)
Records payment transactions for course purchases.

**Key Fields:**
- `id` (UUID, Primary Key)
- `user_id` (Foreign Key to Users)
- `course_id` (Foreign Key to Courses)
- `amount` (Decimal)
- `status` (Enum: pending, processing, completed, failed, cancelled, refunded, partially_refunded)
- `payment_method` (Enum: stripe, paypal, bank_transfer, wallet, free)
- `payment_details` (JSONB) - Payment provider specific data

**Relationships:**
- Many-to-One with Users
- Many-to-One with Courses

## Indexes and Performance

### Primary Indexes
- All tables have UUID primary keys with automatic indexing
- Unique constraints on critical fields (email, subdomain, etc.)

### Performance Indexes
- **Users**: email, role, created_at
- **Schools**: subdomain, owner_id, created_at
- **Courses**: school_id, published, status, level, category, created_at
- **Modules**: course_id, (course_id, order)
- **Lessons**: module_id, content_type, (module_id, order)
- **Enrollments**: user_id, course_id, status, created_at
- **Payments**: user_id, course_id, status, payment_method, created_at

### Composite Indexes
- `(school_id, published)` on courses for efficient school course listing
- `(user_id, status)` on enrollments for user dashboard queries
- `(course_id, status)` on payments for course revenue tracking

### JSON Indexes (GIN)
- JSONB columns have GIN indexes for efficient JSON queries
- Full-text search indexes on title and description fields

## Row-Level Security (RLS)

### Security Model
All tables implement RLS policies to ensure data isolation and proper access control.

### Policy Overview

#### Users Table
- Users can view/update their own profile
- Admins can view/update all users

#### Schools Table
- School owners can manage their schools
- Anyone can view published schools
- Admins can manage all schools

#### Courses Table
- School owners can manage their courses
- Anyone can view published courses
- Enrolled users can view their courses
- Admins can manage all courses

#### Modules & Lessons
- School owners can manage content in their courses
- Enrolled users can view published content
- Free lessons are publicly viewable

#### Enrollments
- Users can view/update their own enrollments
- School owners can view enrollments for their courses
- Admins can manage all enrollments

#### Payments
- Users can view their own payments
- School owners can view payments for their courses
- Admins can view all payments
- System can insert/update payments (for payment processing)

## Supabase Integration

### Project Configuration
- **Project ID**: nxorbfmezftndlzplkob
- **Project URL**: https://nxorbfmezftndlzplkob.supabase.co
- **Database Host**: db.nxorbfmezftndlzplkob.supabase.co

### Supabase Features Used
1. **PostgreSQL Database**: Hosted PostgreSQL with full SQL support
2. **Authentication**: Built-in user authentication with JWT tokens
3. **Row-Level Security**: Database-level security policies
4. **Real-time**: Live data synchronization
5. **Storage**: File and media storage (future use)
6. **Edge Functions**: Serverless functions (future use)

### Migration System

#### Supabase Migrations (Applied)
1. `initial_schema` - Creates tables and enum types
2. `add_foreign_keys` - Adds foreign key constraints
3. `add_indexes` - Creates performance indexes
4. `add_json_and_fulltext_indexes` - JSON and search indexes
5. `enable_rls` - Enables Row-Level Security
6. `add_rls_policies` - Implements security policies
7. `add_enrollment_payment_policies` - Additional RLS policies

#### Running Migrations
Migrations are applied directly to Supabase using the MCP server:
```bash
# Applied via Supabase MCP server
apply_migration_supabase --project_id nxorbfmezftndlzplkob --name migration_name --query "SQL_QUERY"
```

#### TypeORM Migrations (Legacy)
```bash
# Generate new migration from entity changes
npm run migration:generate -- src/migrations/NewMigration

# Show migration status
npm run migration:show
```

## Testing

### Database Tests
Comprehensive test suite covering:
- Entity creation and validation
- Relationship integrity
- Constraint enforcement
- Index performance
- RLS policy functionality

### Running Tests
```bash
# Set up test database
npm run db:setup-test

# Run database tests
npm run test:db

# Validate schema integrity
npm run db:validate
```

### Test Environment
- Separate test database (`arablms_test`)
- Isolated test data
- Automated cleanup between tests

## Development Workflow

### Adding New Entities
1. Create entity file in `src/entities/`
2. Add to `src/entities/index.ts`
3. Generate migration: `npm run migration:generate`
4. Review and run migration: `npm run migration:run`
5. Add tests in `src/test/`
6. Update RLS policies if needed

### Schema Changes
1. Modify entity definitions
2. Generate migration for changes
3. Test migration on development database
4. Update tests and documentation
5. Deploy to staging/production

## Security Considerations

### Data Protection
- All sensitive data is properly encrypted
- RLS policies prevent unauthorized access
- Foreign key constraints maintain referential integrity

### Access Control
- Role-based access through RLS policies
- JWT-based authentication integration
- Audit trails through created_at/updated_at timestamps

### Performance
- Optimized indexes for common query patterns
- JSON indexes for flexible schema fields
- Connection pooling for scalability

## Monitoring and Maintenance

### Performance Monitoring
- Query performance tracking
- Index usage analysis
- Connection pool monitoring

### Backup Strategy
- Regular automated backups
- Point-in-time recovery capability
- Cross-region backup replication

### Maintenance Tasks
- Regular VACUUM and ANALYZE operations
- Index maintenance and optimization
- Statistics updates for query planner
