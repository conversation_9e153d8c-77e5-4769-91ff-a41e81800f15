import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddForeignKeys1703000000001 implements MigrationInterface {
  name = 'AddForeignKeys1703000000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add foreign key constraints
    
    // Schools -> Users (owner)
    await queryRunner.query(`
      ALTER TABLE "schools" 
      ADD CONSTRAINT "FK_schools_owner_id" 
      FOREIGN KEY ("owner_id") REFERENCES "users"("id") 
      ON DELETE CASCADE
    `);

    // Courses -> Schools
    await queryRunner.query(`
      ALTER TABLE "courses" 
      ADD CONSTRAINT "FK_courses_school_id" 
      FOREIGN KEY ("school_id") REFERENCES "schools"("id") 
      ON DELETE CASCADE
    `);

    // Modules -> Courses
    await queryRunner.query(`
      ALTER TABLE "modules" 
      ADD CONSTRAINT "FK_modules_course_id" 
      FOREIGN KEY ("course_id") REFERENCES "courses"("id") 
      ON DELETE CASCADE
    `);

    // Lessons -> Modules
    await queryRunner.query(`
      ALTER TABLE "lessons" 
      ADD CONSTRAINT "FK_lessons_module_id" 
      FOREIGN KEY ("module_id") REFERENCES "modules"("id") 
      ON DELETE CASCADE
    `);

    // Enrollments -> Users
    await queryRunner.query(`
      ALTER TABLE "enrollments" 
      ADD CONSTRAINT "FK_enrollments_user_id" 
      FOREIGN KEY ("user_id") REFERENCES "users"("id") 
      ON DELETE CASCADE
    `);

    // Enrollments -> Courses
    await queryRunner.query(`
      ALTER TABLE "enrollments" 
      ADD CONSTRAINT "FK_enrollments_course_id" 
      FOREIGN KEY ("course_id") REFERENCES "courses"("id") 
      ON DELETE CASCADE
    `);

    // Payments -> Users
    await queryRunner.query(`
      ALTER TABLE "payments" 
      ADD CONSTRAINT "FK_payments_user_id" 
      FOREIGN KEY ("user_id") REFERENCES "users"("id") 
      ON DELETE CASCADE
    `);

    // Payments -> Courses
    await queryRunner.query(`
      ALTER TABLE "payments" 
      ADD CONSTRAINT "FK_payments_course_id" 
      FOREIGN KEY ("course_id") REFERENCES "courses"("id") 
      ON DELETE CASCADE
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove foreign key constraints in reverse order
    await queryRunner.query(`ALTER TABLE "payments" DROP CONSTRAINT "FK_payments_course_id"`);
    await queryRunner.query(`ALTER TABLE "payments" DROP CONSTRAINT "FK_payments_user_id"`);
    await queryRunner.query(`ALTER TABLE "enrollments" DROP CONSTRAINT "FK_enrollments_course_id"`);
    await queryRunner.query(`ALTER TABLE "enrollments" DROP CONSTRAINT "FK_enrollments_user_id"`);
    await queryRunner.query(`ALTER TABLE "lessons" DROP CONSTRAINT "FK_lessons_module_id"`);
    await queryRunner.query(`ALTER TABLE "modules" DROP CONSTRAINT "FK_modules_course_id"`);
    await queryRunner.query(`ALTER TABLE "courses" DROP CONSTRAINT "FK_courses_school_id"`);
    await queryRunner.query(`ALTER TABLE "schools" DROP CONSTRAINT "FK_schools_owner_id"`);
  }
}
