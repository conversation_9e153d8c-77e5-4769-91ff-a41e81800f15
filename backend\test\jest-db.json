{"displayName": "Database Tests", "testMatch": ["<rootDir>/src/test/**/*.test.ts"], "moduleFileExtensions": ["js", "json", "ts"], "rootDir": "../", "testEnvironment": "node", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["src/**/*.(t|j)s", "!src/**/*.spec.ts", "!src/**/*.test.ts"], "coverageDirectory": "coverage/database", "testTimeout": 30000, "setupFilesAfterEnv": ["<rootDir>/test/setup-db-tests.ts"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/src/$1"}}