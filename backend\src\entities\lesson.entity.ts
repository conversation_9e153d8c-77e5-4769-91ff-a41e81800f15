import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Module } from './module.entity';

export enum LessonContentType {
  VIDEO = 'video',
  TEXT = 'text',
  QUIZ = 'quiz',
  ASSIGNMENT = 'assignment',
  DOCUMENT = 'document',
  LIVE_SESSION = 'live_session',
}

export enum LessonStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
}

@Entity('lessons')
@Index(['module_id'])
@Index(['module_id', 'order'])
@Index(['content_type'])
export class Lesson {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: LessonContentType,
    default: LessonContentType.VIDEO,
  })
  content_type: LessonContentType;

  @Column({ type: 'jsonb', nullable: true })
  content: {
    // For VIDEO type
    videoUrl?: string;
    videoDuration?: number;
    videoProvider?: 'bunny' | 'youtube' | 'vimeo';
    videoId?: string;
    
    // For TEXT type
    textContent?: string;
    htmlContent?: string;
    
    // For QUIZ type
    questions?: Array<{
      id: string;
      question: string;
      type: 'multiple_choice' | 'true_false' | 'short_answer';
      options?: string[];
      correctAnswer: string | string[];
      explanation?: string;
    }>;
    
    // For ASSIGNMENT type
    assignmentInstructions?: string;
    submissionFormat?: 'text' | 'file' | 'url';
    maxScore?: number;
    
    // For DOCUMENT type
    documentUrl?: string;
    documentType?: 'pdf' | 'doc' | 'ppt' | 'other';
    
    // For LIVE_SESSION type
    sessionUrl?: string;
    scheduledAt?: string;
    duration?: number;
    
    // Common fields
    attachments?: Array<{
      name: string;
      url: string;
      type: string;
      size: number;
    }>;
    notes?: string;
  };

  @Column({ type: 'integer', default: 0 })
  order: number;

  @Column({
    type: 'enum',
    enum: LessonStatus,
    default: LessonStatus.DRAFT,
  })
  status: LessonStatus;

  @Column({ type: 'boolean', default: true })
  is_published: boolean;

  @Column({ type: 'boolean', default: false })
  is_free: boolean;

  @Column({ type: 'integer', nullable: true })
  duration: number; // in minutes

  @Column({ type: 'jsonb', nullable: true })
  metadata: {
    isLocked?: boolean;
    unlockConditions?: {
      requirePreviousLesson?: boolean;
      requiredLessonIds?: string[];
      minimumScore?: number;
    };
    allowDownload?: boolean;
    allowComments?: boolean;
  };

  @Column({ type: 'uuid' })
  module_id: string;

  @CreateDateColumn({ type: 'timestamp with time zone' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone' })
  updated_at: Date;

  // Relations
  @ManyToOne(() => Module, (module) => module.lessons, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'module_id' })
  module: Module;
}
