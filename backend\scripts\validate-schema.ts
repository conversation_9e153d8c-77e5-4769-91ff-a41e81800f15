import { DataSource } from 'typeorm';
import { config } from 'dotenv';
import { entities } from '../src/entities';

// Load environment variables
config();

interface ValidationResult {
  passed: boolean;
  message: string;
  details?: any;
}

class SchemaValidator {
  private dataSource: DataSource;

  constructor() {
    this.dataSource = new DataSource({
      type: 'postgres',
      url: process.env.DATABASE_URL,
      host: process.env.DATABASE_HOST || 'localhost',
      port: parseInt(process.env.DATABASE_PORT || '5432'),
      username: process.env.DATABASE_USERNAME,
      password: process.env.DATABASE_PASSWORD,
      database: process.env.DATABASE_NAME,
      entities,
      synchronize: false,
      logging: false,
    });
  }

  async connect(): Promise<void> {
    await this.dataSource.initialize();
    console.log('Connected to database for validation');
  }

  async disconnect(): Promise<void> {
    await this.dataSource.destroy();
    console.log('Disconnected from database');
  }

  async validateTables(): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];
    const expectedTables = [
      'users', 'schools', 'courses', 'modules', 
      'lessons', 'enrollments', 'payments', 'typeorm_migrations'
    ];

    try {
      const query = `
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
      `;
      
      const tables = await this.dataSource.query(query);
      const tableNames = tables.map((t: any) => t.table_name);

      for (const expectedTable of expectedTables) {
        if (tableNames.includes(expectedTable)) {
          results.push({
            passed: true,
            message: `Table '${expectedTable}' exists`,
          });
        } else {
          results.push({
            passed: false,
            message: `Table '${expectedTable}' is missing`,
          });
        }
      }
    } catch (error) {
      results.push({
        passed: false,
        message: 'Failed to validate tables',
        details: error.message,
      });
    }

    return results;
  }

  async validateIndexes(): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];
    const expectedIndexes = [
      'IDX_users_email',
      'IDX_schools_subdomain',
      'IDX_courses_school_id',
      'IDX_modules_course_id',
      'IDX_lessons_module_id',
      'IDX_enrollments_user_id',
      'IDX_payments_user_id',
    ];

    try {
      const query = `
        SELECT indexname 
        FROM pg_indexes 
        WHERE schemaname = 'public'
      `;
      
      const indexes = await this.dataSource.query(query);
      const indexNames = indexes.map((i: any) => i.indexname);

      for (const expectedIndex of expectedIndexes) {
        if (indexNames.includes(expectedIndex)) {
          results.push({
            passed: true,
            message: `Index '${expectedIndex}' exists`,
          });
        } else {
          results.push({
            passed: false,
            message: `Index '${expectedIndex}' is missing`,
          });
        }
      }
    } catch (error) {
      results.push({
        passed: false,
        message: 'Failed to validate indexes',
        details: error.message,
      });
    }

    return results;
  }

  async validateConstraints(): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];
    const expectedConstraints = [
      'FK_schools_owner_id',
      'FK_courses_school_id',
      'FK_modules_course_id',
      'FK_lessons_module_id',
      'FK_enrollments_user_id',
      'FK_enrollments_course_id',
      'FK_payments_user_id',
      'FK_payments_course_id',
    ];

    try {
      const query = `
        SELECT constraint_name 
        FROM information_schema.table_constraints 
        WHERE constraint_schema = 'public' 
        AND constraint_type = 'FOREIGN KEY'
      `;
      
      const constraints = await this.dataSource.query(query);
      const constraintNames = constraints.map((c: any) => c.constraint_name);

      for (const expectedConstraint of expectedConstraints) {
        if (constraintNames.includes(expectedConstraint)) {
          results.push({
            passed: true,
            message: `Foreign key constraint '${expectedConstraint}' exists`,
          });
        } else {
          results.push({
            passed: false,
            message: `Foreign key constraint '${expectedConstraint}' is missing`,
          });
        }
      }
    } catch (error) {
      results.push({
        passed: false,
        message: 'Failed to validate constraints',
        details: error.message,
      });
    }

    return results;
  }

  async validateRLSPolicies(): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];
    const expectedTables = ['users', 'schools', 'courses', 'modules', 'lessons', 'enrollments', 'payments'];

    try {
      for (const table of expectedTables) {
        // Check if RLS is enabled
        const rlsQuery = `
          SELECT relrowsecurity 
          FROM pg_class 
          WHERE relname = '${table}'
        `;
        
        const rlsResult = await this.dataSource.query(rlsQuery);
        
        if (rlsResult.length > 0 && rlsResult[0].relrowsecurity) {
          results.push({
            passed: true,
            message: `RLS is enabled for table '${table}'`,
          });

          // Check for policies
          const policiesQuery = `
            SELECT policyname 
            FROM pg_policies 
            WHERE tablename = '${table}'
          `;
          
          const policies = await this.dataSource.query(policiesQuery);
          
          if (policies.length > 0) {
            results.push({
              passed: true,
              message: `Table '${table}' has ${policies.length} RLS policies`,
              details: policies.map((p: any) => p.policyname),
            });
          } else {
            results.push({
              passed: false,
              message: `Table '${table}' has RLS enabled but no policies`,
            });
          }
        } else {
          results.push({
            passed: false,
            message: `RLS is not enabled for table '${table}'`,
          });
        }
      }
    } catch (error) {
      results.push({
        passed: false,
        message: 'Failed to validate RLS policies',
        details: error.message,
      });
    }

    return results;
  }

  async validateEnumTypes(): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];
    const expectedEnums = [
      'user_role_enum',
      'course_status_enum',
      'course_level_enum',
      'lesson_content_type_enum',
      'lesson_status_enum',
      'enrollment_status_enum',
      'payment_status_enum',
      'payment_method_enum',
    ];

    try {
      const query = `
        SELECT typname 
        FROM pg_type 
        WHERE typtype = 'e'
      `;
      
      const enums = await this.dataSource.query(query);
      const enumNames = enums.map((e: any) => e.typname);

      for (const expectedEnum of expectedEnums) {
        if (enumNames.includes(expectedEnum)) {
          results.push({
            passed: true,
            message: `Enum type '${expectedEnum}' exists`,
          });
        } else {
          results.push({
            passed: false,
            message: `Enum type '${expectedEnum}' is missing`,
          });
        }
      }
    } catch (error) {
      results.push({
        passed: false,
        message: 'Failed to validate enum types',
        details: error.message,
      });
    }

    return results;
  }

  async runAllValidations(): Promise<void> {
    console.log('🔍 Starting database schema validation...\n');

    const validations = [
      { name: 'Tables', fn: () => this.validateTables() },
      { name: 'Indexes', fn: () => this.validateIndexes() },
      { name: 'Constraints', fn: () => this.validateConstraints() },
      { name: 'RLS Policies', fn: () => this.validateRLSPolicies() },
      { name: 'Enum Types', fn: () => this.validateEnumTypes() },
    ];

    let totalTests = 0;
    let passedTests = 0;

    for (const validation of validations) {
      console.log(`\n📋 Validating ${validation.name}:`);
      console.log('─'.repeat(50));
      
      const results = await validation.fn();
      
      for (const result of results) {
        totalTests++;
        if (result.passed) {
          passedTests++;
          console.log(`✅ ${result.message}`);
        } else {
          console.log(`❌ ${result.message}`);
          if (result.details) {
            console.log(`   Details: ${JSON.stringify(result.details, null, 2)}`);
          }
        }
      }
    }

    console.log('\n' + '='.repeat(60));
    console.log(`📊 Validation Summary: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
      console.log('🎉 All validations passed! Database schema is correctly implemented.');
    } else {
      console.log('⚠️  Some validations failed. Please review the issues above.');
      process.exit(1);
    }
  }
}

// Run validation if this script is executed directly
if (require.main === module) {
  const validator = new SchemaValidator();
  
  validator.connect()
    .then(() => validator.runAllValidations())
    .then(() => validator.disconnect())
    .catch((error) => {
      console.error('Validation failed:', error);
      process.exit(1);
    });
}

export { SchemaValidator };
