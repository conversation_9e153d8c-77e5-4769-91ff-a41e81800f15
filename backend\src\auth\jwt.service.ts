import {
  Injectable,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SupabaseService } from '../config/supabase.config';
import { User } from '../entities/user.entity';
import { AuthResponseDto, TokenResponseDto } from './dto/token.dto';

export interface JwtPayload {
  sub: string; // user id
  email: string;
  role: string;
  aud: string;
  exp: number;
  iat: number;
  iss: string;
}

@Injectable()
export class JwtService {
  private readonly logger = new Logger(JwtService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly supabaseService: SupabaseService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Validate a Supabase JWT token and return the user
   */
  async validateToken(token: string): Promise<User | null> {
    try {
      // Verify token with Supabase
      const { data: userData, error } = await this.supabaseService.client.auth.getUser(token);

      if (error || !userData.user) {
        this.logger.warn('Invalid token provided', { error: error?.message });
        return null;
      }

      // Get user from local database
      const user = await this.userRepository.findOne({
        where: { id: userData.user.id },
      });

      if (!user) {
        this.logger.warn('User not found in local database', { userId: userData.user.id });
        return null;
      }

      return user;
    } catch (error) {
      this.logger.error('Token validation failed', { error: error.message });
      return null;
    }
  }

  /**
   * Extract user information from a valid Supabase token
   */
  async getUserFromToken(token: string): Promise<User> {
    const user = await this.validateToken(token);
    
    if (!user) {
      throw new UnauthorizedException('Invalid or expired token');
    }

    return user;
  }

  /**
   * Refresh tokens through Supabase
   */
  async refreshTokens(refreshToken: string): Promise<AuthResponseDto> {
    try {
      const { data: authData, error } = await this.supabaseService.client.auth.refreshSession({
        refresh_token: refreshToken,
      });

      if (error || !authData.user || !authData.session) {
        throw new UnauthorizedException('Invalid refresh token');
      }

      // Get user from local database
      const user = await this.userRepository.findOne({
        where: { id: authData.user.id },
      });

      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      return {
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          profileData: user.profile_data,
        },
        tokens: {
          accessToken: authData.session.access_token,
          refreshToken: authData.session.refresh_token,
          tokenType: 'Bearer',
          expiresIn: authData.session.expires_in || 3600,
        },
      };
    } catch (error) {
      this.logger.error('Token refresh failed', { error: error.message });
      throw new UnauthorizedException('Token refresh failed');
    }
  }

  /**
   * Verify token and decode payload without database lookup
   */
  async verifyAndDecode(token: string): Promise<JwtPayload | null> {
    try {
      const { data: userData, error } = await this.supabaseService.client.auth.getUser(token);

      if (error || !userData.user) {
        return null;
      }

      // Extract relevant information from Supabase user
      const payload: JwtPayload = {
        sub: userData.user.id,
        email: userData.user.email || '',
        role: userData.user.user_metadata?.role || 'student',
        aud: userData.user.aud || 'authenticated',
        exp: Math.floor(Date.now() / 1000) + 3600, // Default 1 hour
        iat: Math.floor(Date.now() / 1000),
        iss: 'supabase',
      };

      return payload;
    } catch (error) {
      this.logger.error('Token verification failed', { error: error.message });
      return null;
    }
  }

  /**
   * Extract token from Authorization header
   */
  extractTokenFromHeader(authHeader: string): string | null {
    if (!authHeader) {
      return null;
    }

    const [type, token] = authHeader.split(' ');
    
    if (type !== 'Bearer' || !token) {
      return null;
    }

    return token;
  }

  /**
   * Check if token is expired (client-side check)
   */
  isTokenExpired(payload: JwtPayload): boolean {
    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  }

  /**
   * Get token expiration time in seconds
   */
  getTokenExpirationTime(): number {
    // Supabase default is 1 hour (3600 seconds)
    return 3600;
  }

  /**
   * Get refresh token expiration time in seconds
   */
  getRefreshTokenExpirationTime(): number {
    // Supabase default is 7 days (604800 seconds)
    return 7 * 24 * 60 * 60;
  }

  /**
   * Sanitize user data for token payload (remove sensitive information)
   */
  sanitizeUserForToken(user: User): Partial<User> {
    const { password_hash, ...sanitizedUser } = user;
    return sanitizedUser;
  }

  /**
   * Validate token format (basic validation)
   */
  isValidTokenFormat(token: string): boolean {
    if (!token || typeof token !== 'string') {
      return false;
    }

    // Basic JWT format check (3 parts separated by dots)
    const parts = token.split('.');
    return parts.length === 3;
  }

  /**
   * Log authentication events for security monitoring
   */
  logAuthEvent(event: string, userId?: string, details?: any): void {
    this.logger.log(`Auth Event: ${event}`, {
      userId,
      timestamp: new Date().toISOString(),
      ...details,
    });
  }
}
