import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createClient, SupabaseClient } from '@supabase/supabase-js';

@Injectable()
export class SupabaseService {
  private supabase: SupabaseClient;

  constructor(private configService: ConfigService) {
    const supabaseUrl = this.configService.get<string>('SUPABASE_URL');
    const supabaseKey = this.configService.get<string>('SUPABASE_ANON_KEY');

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Supabase URL and Anon Key must be provided');
    }

    this.supabase = createClient(supabaseUrl, supabaseKey, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: false,
      },
      db: {
        schema: 'public',
      },
      global: {
        headers: {
          'X-Client-Info': 'arablms-backend',
        },
      },
    });
  }

  get client(): SupabaseClient {
    return this.supabase;
  }

  // Helper methods for common operations
  async signUp(email: string, password: string, metadata?: any) {
    return this.supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata,
      },
    });
  }

  async signIn(email: string, password: string) {
    return this.supabase.auth.signInWithPassword({
      email,
      password,
    });
  }

  async signOut() {
    return this.supabase.auth.signOut();
  }

  async getUser(accessToken: string) {
    return this.supabase.auth.getUser(accessToken);
  }

  async updateUser(accessToken: string, updates: any) {
    const { data: { user } } = await this.supabase.auth.getUser(accessToken);
    if (!user) {
      throw new Error('User not found');
    }

    return this.supabase.auth.updateUser(updates);
  }

  // Database operations with RLS
  from(table: string) {
    return this.supabase.from(table);
  }

  // Storage operations
  storage() {
    return this.supabase.storage;
  }

  // Real-time subscriptions
  channel(name: string) {
    return this.supabase.channel(name);
  }

  // Execute raw SQL (for admin operations)
  async rpc(functionName: string, params?: any) {
    return this.supabase.rpc(functionName, params);
  }
}

// Database types for TypeScript support
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          password_hash: string;
          role: 'student' | 'instructor' | 'admin';
          profile_data: any;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          email: string;
          password_hash: string;
          role?: 'student' | 'instructor' | 'admin';
          profile_data?: any;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          password_hash?: string;
          role?: 'student' | 'instructor' | 'admin';
          profile_data?: any;
          created_at?: string;
          updated_at?: string;
        };
      };
      schools: {
        Row: {
          id: string;
          name: string;
          subdomain: string;
          logo_url: string | null;
          primary_color: string;
          description: string | null;
          settings: any;
          owner_id: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          subdomain: string;
          logo_url?: string | null;
          primary_color?: string;
          description?: string | null;
          settings?: any;
          owner_id: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          subdomain?: string;
          logo_url?: string | null;
          primary_color?: string;
          description?: string | null;
          settings?: any;
          owner_id?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      courses: {
        Row: {
          id: string;
          title: string;
          description: string | null;
          short_description: string | null;
          price: number;
          thumbnail_url: string | null;
          preview_video_url: string | null;
          status: 'draft' | 'published' | 'archived';
          published: boolean;
          level: 'beginner' | 'intermediate' | 'advanced';
          category: string | null;
          tags: string[];
          metadata: any;
          school_id: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          title: string;
          description?: string | null;
          short_description?: string | null;
          price?: number;
          thumbnail_url?: string | null;
          preview_video_url?: string | null;
          status?: 'draft' | 'published' | 'archived';
          published?: boolean;
          level?: 'beginner' | 'intermediate' | 'advanced';
          category?: string | null;
          tags?: string[];
          metadata?: any;
          school_id: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          title?: string;
          description?: string | null;
          short_description?: string | null;
          price?: number;
          thumbnail_url?: string | null;
          preview_video_url?: string | null;
          status?: 'draft' | 'published' | 'archived';
          published?: boolean;
          level?: 'beginner' | 'intermediate' | 'advanced';
          category?: string | null;
          tags?: string[];
          metadata?: any;
          school_id?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      // Add other table types as needed...
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      user_role_enum: 'student' | 'instructor' | 'admin';
      course_status_enum: 'draft' | 'published' | 'archived';
      course_level_enum: 'beginner' | 'intermediate' | 'advanced';
      lesson_content_type_enum: 'video' | 'text' | 'quiz' | 'assignment' | 'document' | 'live_session';
      lesson_status_enum: 'draft' | 'published' | 'archived';
      enrollment_status_enum: 'active' | 'completed' | 'suspended' | 'cancelled';
      payment_status_enum: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'refunded' | 'partially_refunded';
      payment_method_enum: 'stripe' | 'paypal' | 'bank_transfer' | 'wallet' | 'free';
    };
  };
}
