import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRLSPolicies1703000000003 implements MigrationInterface {
  name = 'AddRLSPolicies1703000000003';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Enable RLS on all tables
    await queryRunner.query(`ALTER TABLE "users" ENABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`ALTER TABLE "schools" ENABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`ALTER TABLE "courses" ENABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`ALTER TABLE "modules" ENABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`ALTER TABLE "lessons" ENABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`ALTER TABLE "enrollments" ENABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`ALTER TABLE "payments" ENABLE ROW LEVEL SECURITY`);

    // Create helper functions for RLS
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION auth.user_id() RETURNS uuid AS $$
        SELECT COALESCE(
          current_setting('request.jwt.claims', true)::json->>'sub',
          current_setting('request.jwt.claims', true)::json->>'user_id'
        )::uuid;
      $$ LANGUAGE sql STABLE;
    `);

    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION auth.user_role() RETURNS text AS $$
        SELECT COALESCE(
          current_setting('request.jwt.claims', true)::json->>'role',
          'student'
        );
      $$ LANGUAGE sql STABLE;
    `);

    // Users table policies
    await queryRunner.query(`
      CREATE POLICY "Users can view their own profile" ON "users"
      FOR SELECT USING (auth.user_id() = id);
    `);

    await queryRunner.query(`
      CREATE POLICY "Users can update their own profile" ON "users"
      FOR UPDATE USING (auth.user_id() = id);
    `);

    await queryRunner.query(`
      CREATE POLICY "Admins can view all users" ON "users"
      FOR SELECT USING (auth.user_role() = 'admin');
    `);

    await queryRunner.query(`
      CREATE POLICY "Admins can update all users" ON "users"
      FOR UPDATE USING (auth.user_role() = 'admin');
    `);

    // Schools table policies
    await queryRunner.query(`
      CREATE POLICY "School owners can manage their schools" ON "schools"
      FOR ALL USING (auth.user_id() = owner_id);
    `);

    await queryRunner.query(`
      CREATE POLICY "Anyone can view published schools" ON "schools"
      FOR SELECT USING (true);
    `);

    await queryRunner.query(`
      CREATE POLICY "Admins can manage all schools" ON "schools"
      FOR ALL USING (auth.user_role() = 'admin');
    `);

    // Courses table policies
    await queryRunner.query(`
      CREATE POLICY "School owners can manage their courses" ON "courses"
      FOR ALL USING (
        school_id IN (
          SELECT id FROM schools WHERE owner_id = auth.user_id()
        )
      );
    `);

    await queryRunner.query(`
      CREATE POLICY "Anyone can view published courses" ON "courses"
      FOR SELECT USING (published = true);
    `);

    await queryRunner.query(`
      CREATE POLICY "Enrolled users can view courses" ON "courses"
      FOR SELECT USING (
        id IN (
          SELECT course_id FROM enrollments 
          WHERE user_id = auth.user_id() AND status = 'active'
        )
      );
    `);

    await queryRunner.query(`
      CREATE POLICY "Admins can manage all courses" ON "courses"
      FOR ALL USING (auth.user_role() = 'admin');
    `);

    // Modules table policies
    await queryRunner.query(`
      CREATE POLICY "School owners can manage their modules" ON "modules"
      FOR ALL USING (
        course_id IN (
          SELECT c.id FROM courses c
          JOIN schools s ON c.school_id = s.id
          WHERE s.owner_id = auth.user_id()
        )
      );
    `);

    await queryRunner.query(`
      CREATE POLICY "Enrolled users can view published modules" ON "modules"
      FOR SELECT USING (
        is_published = true AND
        course_id IN (
          SELECT course_id FROM enrollments 
          WHERE user_id = auth.user_id() AND status = 'active'
        )
      );
    `);

    await queryRunner.query(`
      CREATE POLICY "Anyone can view modules of published courses" ON "modules"
      FOR SELECT USING (
        is_published = true AND
        course_id IN (
          SELECT id FROM courses WHERE published = true
        )
      );
    `);

    // Lessons table policies
    await queryRunner.query(`
      CREATE POLICY "School owners can manage their lessons" ON "lessons"
      FOR ALL USING (
        module_id IN (
          SELECT m.id FROM modules m
          JOIN courses c ON m.course_id = c.id
          JOIN schools s ON c.school_id = s.id
          WHERE s.owner_id = auth.user_id()
        )
      );
    `);

    await queryRunner.query(`
      CREATE POLICY "Enrolled users can view published lessons" ON "lessons"
      FOR SELECT USING (
        is_published = true AND
        module_id IN (
          SELECT m.id FROM modules m
          JOIN enrollments e ON m.course_id = e.course_id
          WHERE e.user_id = auth.user_id() AND e.status = 'active'
        )
      );
    `);

    await queryRunner.query(`
      CREATE POLICY "Anyone can view free lessons of published courses" ON "lessons"
      FOR SELECT USING (
        is_published = true AND is_free = true AND
        module_id IN (
          SELECT m.id FROM modules m
          JOIN courses c ON m.course_id = c.id
          WHERE c.published = true
        )
      );
    `);

    // Enrollments table policies
    await queryRunner.query(`
      CREATE POLICY "Users can view their own enrollments" ON "enrollments"
      FOR SELECT USING (auth.user_id() = user_id);
    `);

    await queryRunner.query(`
      CREATE POLICY "Users can update their own enrollment progress" ON "enrollments"
      FOR UPDATE USING (auth.user_id() = user_id);
    `);

    await queryRunner.query(`
      CREATE POLICY "School owners can view enrollments for their courses" ON "enrollments"
      FOR SELECT USING (
        course_id IN (
          SELECT c.id FROM courses c
          JOIN schools s ON c.school_id = s.id
          WHERE s.owner_id = auth.user_id()
        )
      );
    `);

    await queryRunner.query(`
      CREATE POLICY "Admins can manage all enrollments" ON "enrollments"
      FOR ALL USING (auth.user_role() = 'admin');
    `);

    // Payments table policies
    await queryRunner.query(`
      CREATE POLICY "Users can view their own payments" ON "payments"
      FOR SELECT USING (auth.user_id() = user_id);
    `);

    await queryRunner.query(`
      CREATE POLICY "School owners can view payments for their courses" ON "payments"
      FOR SELECT USING (
        course_id IN (
          SELECT c.id FROM courses c
          JOIN schools s ON c.school_id = s.id
          WHERE s.owner_id = auth.user_id()
        )
      );
    `);

    await queryRunner.query(`
      CREATE POLICY "Admins can view all payments" ON "payments"
      FOR SELECT USING (auth.user_role() = 'admin');
    `);

    await queryRunner.query(`
      CREATE POLICY "System can insert payments" ON "payments"
      FOR INSERT WITH CHECK (true);
    `);

    await queryRunner.query(`
      CREATE POLICY "System can update payments" ON "payments"
      FOR UPDATE USING (true);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop all policies
    await queryRunner.query(`DROP POLICY IF EXISTS "System can update payments" ON "payments"`);
    await queryRunner.query(`DROP POLICY IF EXISTS "System can insert payments" ON "payments"`);
    await queryRunner.query(`DROP POLICY IF EXISTS "Admins can view all payments" ON "payments"`);
    await queryRunner.query(`DROP POLICY IF EXISTS "School owners can view payments for their courses" ON "payments"`);
    await queryRunner.query(`DROP POLICY IF EXISTS "Users can view their own payments" ON "payments"`);

    await queryRunner.query(`DROP POLICY IF EXISTS "Admins can manage all enrollments" ON "enrollments"`);
    await queryRunner.query(`DROP POLICY IF EXISTS "School owners can view enrollments for their courses" ON "enrollments"`);
    await queryRunner.query(`DROP POLICY IF EXISTS "Users can update their own enrollment progress" ON "enrollments"`);
    await queryRunner.query(`DROP POLICY IF EXISTS "Users can view their own enrollments" ON "enrollments"`);

    await queryRunner.query(`DROP POLICY IF EXISTS "Anyone can view free lessons of published courses" ON "lessons"`);
    await queryRunner.query(`DROP POLICY IF EXISTS "Enrolled users can view published lessons" ON "lessons"`);
    await queryRunner.query(`DROP POLICY IF EXISTS "School owners can manage their lessons" ON "lessons"`);

    await queryRunner.query(`DROP POLICY IF EXISTS "Anyone can view modules of published courses" ON "modules"`);
    await queryRunner.query(`DROP POLICY IF EXISTS "Enrolled users can view published modules" ON "modules"`);
    await queryRunner.query(`DROP POLICY IF EXISTS "School owners can manage their modules" ON "modules"`);

    await queryRunner.query(`DROP POLICY IF EXISTS "Admins can manage all courses" ON "courses"`);
    await queryRunner.query(`DROP POLICY IF EXISTS "Enrolled users can view courses" ON "courses"`);
    await queryRunner.query(`DROP POLICY IF EXISTS "Anyone can view published courses" ON "courses"`);
    await queryRunner.query(`DROP POLICY IF EXISTS "School owners can manage their courses" ON "courses"`);

    await queryRunner.query(`DROP POLICY IF EXISTS "Admins can manage all schools" ON "schools"`);
    await queryRunner.query(`DROP POLICY IF EXISTS "Anyone can view published schools" ON "schools"`);
    await queryRunner.query(`DROP POLICY IF EXISTS "School owners can manage their schools" ON "schools"`);

    await queryRunner.query(`DROP POLICY IF EXISTS "Admins can update all users" ON "users"`);
    await queryRunner.query(`DROP POLICY IF EXISTS "Admins can view all users" ON "users"`);
    await queryRunner.query(`DROP POLICY IF EXISTS "Users can update their own profile" ON "users"`);
    await queryRunner.query(`DROP POLICY IF EXISTS "Users can view their own profile" ON "users"`);

    // Drop helper functions
    await queryRunner.query(`DROP FUNCTION IF EXISTS auth.user_role()`);
    await queryRunner.query(`DROP FUNCTION IF EXISTS auth.user_id()`);

    // Disable RLS on all tables
    await queryRunner.query(`ALTER TABLE "payments" DISABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`ALTER TABLE "enrollments" DISABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`ALTER TABLE "lessons" DISABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`ALTER TABLE "modules" DISABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`ALTER TABLE "courses" DISABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`ALTER TABLE "schools" DISABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`ALTER TABLE "users" DISABLE ROW LEVEL SECURITY`);
  }
}
