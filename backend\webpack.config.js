const path = require('path');

module.exports = {
  entry: './src/main.ts',
  target: 'node',
  mode: 'development',
  externals: {
    'cloudflare:sockets': 'commonjs cloudflare:sockets',
    'mock-aws-s3': 'commonjs mock-aws-s3',
    'aws-sdk': 'commonjs aws-sdk',
    'nock': 'commonjs nock',
  },
  module: {
    rules: [
      {
        test: /\.ts$/,
        use: 'ts-loader',
        exclude: /node_modules/,
      },
    ],
  },
  resolve: {
    extensions: ['.ts', '.js'],
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'main.js',
  },
  ignoreWarnings: [
    {
      module: /node_modules/,
    },
  ],
};
