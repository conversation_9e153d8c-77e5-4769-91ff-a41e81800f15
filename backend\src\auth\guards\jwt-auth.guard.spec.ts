import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtAuthGuard } from './jwt-auth.guard';
import { JwtService } from '../jwt.service';
import { User, UserRole } from '../../entities/user.entity';

describe('JwtAuthGuard', () => {
  let guard: JwtAuthGuard;
  let jwtService: JwtService;
  let reflector: Reflector;

  const mockUser: User = {
    id: '123',
    email: '<EMAIL>',
    password_hash: 'hashed',
    role: UserRole.STUDENT,
    profile_data: {},
    created_at: new Date(),
    updated_at: new Date(),
    owned_schools: [],
    enrollments: [],
    payments: [],
  };

  const mockJwtService = {
    extractTokenFromHeader: jest.fn(),
    isValidTokenFormat: jest.fn(),
    validateToken: jest.fn(),
    logAuthEvent: jest.fn(),
  };

  const mockReflector = {
    getAllAndOverride: jest.fn(),
  };

  const createMockExecutionContext = (headers: any = {}, route: any = {}): ExecutionContext => ({
    switchToHttp: () => ({
      getRequest: () => ({
        headers,
        route,
        method: 'GET',
        user: null,
      }),
    }),
    getHandler: jest.fn(),
    getClass: jest.fn(),
  } as any);

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        JwtAuthGuard,
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: Reflector,
          useValue: mockReflector,
        },
      ],
    }).compile();

    guard = module.get<JwtAuthGuard>(JwtAuthGuard);
    jwtService = module.get<JwtService>(JwtService);
    reflector = module.get<Reflector>(Reflector);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  describe('canActivate', () => {
    it('should allow access to public routes', async () => {
      mockReflector.getAllAndOverride.mockReturnValue(true);
      const context = createMockExecutionContext();

      const result = await guard.canActivate(context);

      expect(result).toBe(true);
      expect(mockReflector.getAllAndOverride).toHaveBeenCalledWith('isPublic', [
        context.getHandler(),
        context.getClass(),
      ]);
    });

    it('should throw UnauthorizedException when no authorization header', async () => {
      mockReflector.getAllAndOverride.mockReturnValue(false);
      const context = createMockExecutionContext({});

      await expect(guard.canActivate(context)).rejects.toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException for invalid token format', async () => {
      mockReflector.getAllAndOverride.mockReturnValue(false);
      const context = createMockExecutionContext({
        authorization: 'Bearer invalid-token',
      });

      mockJwtService.extractTokenFromHeader.mockReturnValue('invalid-token');
      mockJwtService.isValidTokenFormat.mockReturnValue(false);

      await expect(guard.canActivate(context)).rejects.toThrow(UnauthorizedException);
    });

    it('should allow access for valid token', async () => {
      mockReflector.getAllAndOverride.mockReturnValue(false);
      const context = createMockExecutionContext({
        authorization: 'Bearer valid-token',
      });

      mockJwtService.extractTokenFromHeader.mockReturnValue('valid-token');
      mockJwtService.isValidTokenFormat.mockReturnValue(true);
      mockJwtService.validateToken.mockResolvedValue(mockUser);

      const result = await guard.canActivate(context);

      expect(result).toBe(true);
      expect(mockJwtService.logAuthEvent).toHaveBeenCalledWith(
        'GUARD_VALIDATION_SUCCESS',
        mockUser.id,
        expect.any(Object),
      );
    });

    it('should throw UnauthorizedException for invalid token', async () => {
      mockReflector.getAllAndOverride.mockReturnValue(false);
      const context = createMockExecutionContext({
        authorization: 'Bearer invalid-token',
      });

      mockJwtService.extractTokenFromHeader.mockReturnValue('invalid-token');
      mockJwtService.isValidTokenFormat.mockReturnValue(true);
      mockJwtService.validateToken.mockResolvedValue(null);

      await expect(guard.canActivate(context)).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('handleRequest', () => {
    it('should return user when authentication succeeds', () => {
      const context = createMockExecutionContext();
      const result = guard.handleRequest(null, mockUser, null, context);

      expect(result).toEqual(mockUser);
    });

    it('should throw UnauthorizedException when user is null', () => {
      const context = createMockExecutionContext();

      expect(() => guard.handleRequest(null, null, null, context)).toThrow(
        UnauthorizedException,
      );
    });

    it('should throw error when error is provided', () => {
      const context = createMockExecutionContext();
      const error = new Error('Test error');

      expect(() => guard.handleRequest(error, mockUser, null, context)).toThrow(error);
    });
  });
});
