# Test Database Configuration
DATABASE_URL="postgresql://test_user:test_password@localhost:5432/arablms_test"
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=test_user
DATABASE_PASSWORD=test_password
DATABASE_NAME=arablms_test

# JWT Configuration
JWT_SECRET=test-jwt-secret-key-for-testing
JWT_EXPIRES_IN=7d

# Application Configuration
NODE_ENV=test
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:3001

# External Services (Test/Mock)
STRIPE_SECRET_KEY=sk_test_mock_key
STRIPE_PUBLISHABLE_KEY=pk_test_mock_key
STRIPE_WEBHOOK_SECRET=whsec_test_mock_secret

# File Storage (Test/Mock)
SUPABASE_URL=https://test-project.supabase.co
SUPABASE_ANON_KEY=test_anon_key
SUPABASE_SERVICE_ROLE_KEY=test_service_role_key

# Video Hosting (Test/Mock)
BUNNY_API_KEY=test_bunny_api_key
BUNNY_STORAGE_ZONE=test_storage_zone
BUNNY_CDN_URL=https://test-cdn-url.b-cdn.net

# Email Configuration (Test/Mock)
SMTP_HOST=smtp.test.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=test_password
