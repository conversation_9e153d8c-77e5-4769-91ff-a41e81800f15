import { DataSource } from 'typeorm';
import { config } from 'dotenv';
import { entities } from '../src/entities';

// Load test environment variables
config({ path: '.env.test' });

const setupTestDatabase = async () => {
  console.log('Setting up test database...');

  // Create connection to PostgreSQL server (without specific database)
  const adminDataSource = new DataSource({
    type: 'postgres',
    host: process.env.DATABASE_HOST || 'localhost',
    port: parseInt(process.env.DATABASE_PORT || '5432'),
    username: process.env.DATABASE_USERNAME,
    password: process.env.DATABASE_PASSWORD,
    database: 'postgres', // Connect to default postgres database
  });

  try {
    await adminDataSource.initialize();
    console.log('Connected to PostgreSQL server');

    // Drop test database if it exists
    await adminDataSource.query(`DROP DATABASE IF EXISTS ${process.env.DATABASE_NAME}`);
    console.log(`Dropped database ${process.env.DATABASE_NAME} if it existed`);

    // Create test database
    await adminDataSource.query(`CREATE DATABASE ${process.env.DATABASE_NAME}`);
    console.log(`Created database ${process.env.DATABASE_NAME}`);

    await adminDataSource.destroy();

    // Connect to the test database and run migrations
    const testDataSource = new DataSource({
      type: 'postgres',
      host: process.env.DATABASE_HOST || 'localhost',
      port: parseInt(process.env.DATABASE_PORT || '5432'),
      username: process.env.DATABASE_USERNAME,
      password: process.env.DATABASE_PASSWORD,
      database: process.env.DATABASE_NAME,
      entities,
      migrations: ['src/migrations/*.ts'],
      synchronize: false,
      logging: false,
    });

    await testDataSource.initialize();
    console.log('Connected to test database');

    // Run migrations
    await testDataSource.runMigrations();
    console.log('Migrations completed successfully');

    await testDataSource.destroy();
    console.log('Test database setup completed successfully!');

  } catch (error) {
    console.error('Error setting up test database:', error);
    process.exit(1);
  }
};

// Run the setup if this script is executed directly
if (require.main === module) {
  setupTestDatabase();
}

export { setupTestDatabase };
