import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIndexes1703000000002 implements MigrationInterface {
  name = 'AddIndexes1703000000002';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Users table indexes
    await queryRunner.query(`CREATE INDEX "IDX_users_email" ON "users" ("email")`);
    await queryRunner.query(`CREATE INDEX "IDX_users_role" ON "users" ("role")`);
    await queryRunner.query(`CREATE INDEX "IDX_users_created_at" ON "users" ("created_at")`);

    // Schools table indexes
    await queryRunner.query(`CREATE INDEX "IDX_schools_subdomain" ON "schools" ("subdomain")`);
    await queryRunner.query(`CREATE INDEX "IDX_schools_owner_id" ON "schools" ("owner_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_schools_created_at" ON "schools" ("created_at")`);

    // Courses table indexes
    await queryRunner.query(`CREATE INDEX "IDX_courses_school_id" ON "courses" ("school_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_courses_published" ON "courses" ("published")`);
    await queryRunner.query(`CREATE INDEX "IDX_courses_status" ON "courses" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_courses_level" ON "courses" ("level")`);
    await queryRunner.query(`CREATE INDEX "IDX_courses_category" ON "courses" ("category")`);
    await queryRunner.query(`CREATE INDEX "IDX_courses_created_at" ON "courses" ("created_at")`);
    await queryRunner.query(`CREATE INDEX "IDX_courses_price" ON "courses" ("price")`);
    
    // Composite index for course filtering
    await queryRunner.query(`CREATE INDEX "IDX_courses_school_published" ON "courses" ("school_id", "published")`);
    await queryRunner.query(`CREATE INDEX "IDX_courses_published_level" ON "courses" ("published", "level")`);

    // Modules table indexes
    await queryRunner.query(`CREATE INDEX "IDX_modules_course_id" ON "modules" ("course_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_modules_is_published" ON "modules" ("is_published")`);
    await queryRunner.query(`CREATE INDEX "IDX_modules_course_order" ON "modules" ("course_id", "order")`);

    // Lessons table indexes
    await queryRunner.query(`CREATE INDEX "IDX_lessons_module_id" ON "lessons" ("module_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_lessons_content_type" ON "lessons" ("content_type")`);
    await queryRunner.query(`CREATE INDEX "IDX_lessons_status" ON "lessons" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_lessons_is_published" ON "lessons" ("is_published")`);
    await queryRunner.query(`CREATE INDEX "IDX_lessons_is_free" ON "lessons" ("is_free")`);
    await queryRunner.query(`CREATE INDEX "IDX_lessons_module_order" ON "lessons" ("module_id", "order")`);

    // Enrollments table indexes
    await queryRunner.query(`CREATE INDEX "IDX_enrollments_user_id" ON "enrollments" ("user_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_enrollments_course_id" ON "enrollments" ("course_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_enrollments_status" ON "enrollments" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_enrollments_created_at" ON "enrollments" ("created_at")`);
    await queryRunner.query(`CREATE INDEX "IDX_enrollments_completed_at" ON "enrollments" ("completed_at")`);
    
    // Composite indexes for enrollment queries
    await queryRunner.query(`CREATE INDEX "IDX_enrollments_user_status" ON "enrollments" ("user_id", "status")`);
    await queryRunner.query(`CREATE INDEX "IDX_enrollments_course_status" ON "enrollments" ("course_id", "status")`);

    // Payments table indexes
    await queryRunner.query(`CREATE INDEX "IDX_payments_user_id" ON "payments" ("user_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_payments_course_id" ON "payments" ("course_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_payments_status" ON "payments" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_payments_payment_method" ON "payments" ("payment_method")`);
    await queryRunner.query(`CREATE INDEX "IDX_payments_stripe_payment_id" ON "payments" ("stripe_payment_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_payments_created_at" ON "payments" ("created_at")`);
    await queryRunner.query(`CREATE INDEX "IDX_payments_paid_at" ON "payments" ("paid_at")`);
    
    // Composite indexes for payment queries
    await queryRunner.query(`CREATE INDEX "IDX_payments_user_status" ON "payments" ("user_id", "status")`);
    await queryRunner.query(`CREATE INDEX "IDX_payments_course_status" ON "payments" ("course_id", "status")`);

    // Performance indexes for JSON columns (PostgreSQL specific)
    await queryRunner.query(`CREATE INDEX "IDX_users_profile_data_gin" ON "users" USING gin ("profile_data")`);
    await queryRunner.query(`CREATE INDEX "IDX_schools_settings_gin" ON "schools" USING gin ("settings")`);
    await queryRunner.query(`CREATE INDEX "IDX_courses_metadata_gin" ON "courses" USING gin ("metadata")`);
    await queryRunner.query(`CREATE INDEX "IDX_courses_tags_gin" ON "courses" USING gin ("tags")`);
    await queryRunner.query(`CREATE INDEX "IDX_modules_metadata_gin" ON "modules" USING gin ("metadata")`);
    await queryRunner.query(`CREATE INDEX "IDX_lessons_content_gin" ON "lessons" USING gin ("content")`);
    await queryRunner.query(`CREATE INDEX "IDX_lessons_metadata_gin" ON "lessons" USING gin ("metadata")`);
    await queryRunner.query(`CREATE INDEX "IDX_enrollments_progress_gin" ON "enrollments" USING gin ("progress")`);
    await queryRunner.query(`CREATE INDEX "IDX_payments_payment_details_gin" ON "payments" USING gin ("payment_details")`);

    // Full-text search indexes
    await queryRunner.query(`
      CREATE INDEX "IDX_courses_title_fulltext" ON "courses" 
      USING gin(to_tsvector('english', "title"))
    `);
    
    await queryRunner.query(`
      CREATE INDEX "IDX_courses_description_fulltext" ON "courses" 
      USING gin(to_tsvector('english', "description"))
    `);
    
    await queryRunner.query(`
      CREATE INDEX "IDX_lessons_title_fulltext" ON "lessons" 
      USING gin(to_tsvector('english', "title"))
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop full-text search indexes
    await queryRunner.query(`DROP INDEX "IDX_lessons_title_fulltext"`);
    await queryRunner.query(`DROP INDEX "IDX_courses_description_fulltext"`);
    await queryRunner.query(`DROP INDEX "IDX_courses_title_fulltext"`);

    // Drop JSON indexes
    await queryRunner.query(`DROP INDEX "IDX_payments_payment_details_gin"`);
    await queryRunner.query(`DROP INDEX "IDX_enrollments_progress_gin"`);
    await queryRunner.query(`DROP INDEX "IDX_lessons_metadata_gin"`);
    await queryRunner.query(`DROP INDEX "IDX_lessons_content_gin"`);
    await queryRunner.query(`DROP INDEX "IDX_modules_metadata_gin"`);
    await queryRunner.query(`DROP INDEX "IDX_courses_tags_gin"`);
    await queryRunner.query(`DROP INDEX "IDX_courses_metadata_gin"`);
    await queryRunner.query(`DROP INDEX "IDX_schools_settings_gin"`);
    await queryRunner.query(`DROP INDEX "IDX_users_profile_data_gin"`);

    // Drop payment indexes
    await queryRunner.query(`DROP INDEX "IDX_payments_course_status"`);
    await queryRunner.query(`DROP INDEX "IDX_payments_user_status"`);
    await queryRunner.query(`DROP INDEX "IDX_payments_paid_at"`);
    await queryRunner.query(`DROP INDEX "IDX_payments_created_at"`);
    await queryRunner.query(`DROP INDEX "IDX_payments_stripe_payment_id"`);
    await queryRunner.query(`DROP INDEX "IDX_payments_payment_method"`);
    await queryRunner.query(`DROP INDEX "IDX_payments_status"`);
    await queryRunner.query(`DROP INDEX "IDX_payments_course_id"`);
    await queryRunner.query(`DROP INDEX "IDX_payments_user_id"`);

    // Drop enrollment indexes
    await queryRunner.query(`DROP INDEX "IDX_enrollments_course_status"`);
    await queryRunner.query(`DROP INDEX "IDX_enrollments_user_status"`);
    await queryRunner.query(`DROP INDEX "IDX_enrollments_completed_at"`);
    await queryRunner.query(`DROP INDEX "IDX_enrollments_created_at"`);
    await queryRunner.query(`DROP INDEX "IDX_enrollments_status"`);
    await queryRunner.query(`DROP INDEX "IDX_enrollments_course_id"`);
    await queryRunner.query(`DROP INDEX "IDX_enrollments_user_id"`);

    // Drop lesson indexes
    await queryRunner.query(`DROP INDEX "IDX_lessons_module_order"`);
    await queryRunner.query(`DROP INDEX "IDX_lessons_is_free"`);
    await queryRunner.query(`DROP INDEX "IDX_lessons_is_published"`);
    await queryRunner.query(`DROP INDEX "IDX_lessons_status"`);
    await queryRunner.query(`DROP INDEX "IDX_lessons_content_type"`);
    await queryRunner.query(`DROP INDEX "IDX_lessons_module_id"`);

    // Drop module indexes
    await queryRunner.query(`DROP INDEX "IDX_modules_course_order"`);
    await queryRunner.query(`DROP INDEX "IDX_modules_is_published"`);
    await queryRunner.query(`DROP INDEX "IDX_modules_course_id"`);

    // Drop course indexes
    await queryRunner.query(`DROP INDEX "IDX_courses_published_level"`);
    await queryRunner.query(`DROP INDEX "IDX_courses_school_published"`);
    await queryRunner.query(`DROP INDEX "IDX_courses_price"`);
    await queryRunner.query(`DROP INDEX "IDX_courses_created_at"`);
    await queryRunner.query(`DROP INDEX "IDX_courses_category"`);
    await queryRunner.query(`DROP INDEX "IDX_courses_level"`);
    await queryRunner.query(`DROP INDEX "IDX_courses_status"`);
    await queryRunner.query(`DROP INDEX "IDX_courses_published"`);
    await queryRunner.query(`DROP INDEX "IDX_courses_school_id"`);

    // Drop school indexes
    await queryRunner.query(`DROP INDEX "IDX_schools_created_at"`);
    await queryRunner.query(`DROP INDEX "IDX_schools_owner_id"`);
    await queryRunner.query(`DROP INDEX "IDX_schools_subdomain"`);

    // Drop user indexes
    await queryRunner.query(`DROP INDEX "IDX_users_created_at"`);
    await queryRunner.query(`DROP INDEX "IDX_users_role"`);
    await queryRunner.query(`DROP INDEX "IDX_users_email"`);
  }
}
