import { setupTestDatabase } from '../scripts/setup-test-db';

// Global setup for database tests
beforeAll(async () => {
  // Set up test database before running tests
  await setupTestDatabase();
}, 60000); // 60 second timeout for database setup

// Global teardown
afterAll(async () => {
  // Clean up after all tests
  console.log('Database tests completed');
});

// Increase timeout for database operations
jest.setTimeout(30000);
