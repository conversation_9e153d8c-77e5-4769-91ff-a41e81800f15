const express = require('express');
const cors = require('cors');

const app = express();

// Enable CORS
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true,
}));

// Parse JSON bodies
app.use(express.json());

// Test routes for our authentication system
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'ArabLMS Backend is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Mock authentication endpoints
app.post('/api/auth/register', (req, res) => {
  const { email, password, role } = req.body;

  if (!email || !password) {
    return res.status(400).json({
      statusCode: 400,
      message: ['Email and password are required'],
      error: 'Bad Request'
    });
  }

  // Mock successful registration
  res.status(201).json({
    user: {
      id: 'mock-user-id-123',
      email: email,
      role: role || 'student',
      profileData: {}
    },
    tokens: {
      accessToken: 'mock-access-token',
      refreshToken: 'mock-refresh-token',
      tokenType: 'Bearer',
      expiresIn: 900
    }
  });
});

app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;

  if (!email || !password) {
    return res.status(400).json({
      statusCode: 400,
      message: ['Email and password are required'],
      error: 'Bad Request'
    });
  }

  // Mock successful login
  res.json({
    user: {
      id: 'mock-user-id-123',
      email: email,
      role: 'student',
      profileData: {}
    },
    tokens: {
      accessToken: 'mock-access-token',
      refreshToken: 'mock-refresh-token',
      tokenType: 'Bearer',
      expiresIn: 900
    }
  });
});

app.post('/api/auth/refresh-token', (req, res) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    return res.status(401).json({
      statusCode: 401,
      message: 'Refresh token is required',
      error: 'Unauthorized'
    });
  }

  // Mock token refresh
  res.json({
    user: {
      id: 'mock-user-id-123',
      email: '<EMAIL>',
      role: 'student',
      profileData: {}
    },
    tokens: {
      accessToken: 'new-mock-access-token',
      refreshToken: 'new-mock-refresh-token',
      tokenType: 'Bearer',
      expiresIn: 900
    }
  });
});

app.post('/api/auth/logout', (req, res) => {
  res.json({ message: 'Logged out successfully' });
});

app.get('/api/auth/profile', (req, res) => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      statusCode: 401,
      message: 'Authorization header is required',
      error: 'Unauthorized'
    });
  }

  // Mock profile response
  res.json({
    id: 'mock-user-id-123',
    email: '<EMAIL>',
    role: 'student',
    profileData: {},
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  });
});

// RBAC example endpoints
app.get('/api/rbac-examples/admin-only', (req, res) => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      statusCode: 401,
      message: 'Authorization header is required',
      error: 'Unauthorized'
    });
  }

  // Mock admin check (in real implementation, this would check the token)
  res.json({
    message: 'This route is accessible only by admins',
    user: {
      id: 'mock-user-id-123',
      email: '<EMAIL>',
      role: 'admin'
    }
  });
});

app.get('/api/rbac-examples/authenticated', (req, res) => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      statusCode: 401,
      message: 'Authorization header is required',
      error: 'Unauthorized'
    });
  }

  res.json({
    message: 'This route is accessible by any authenticated user',
    user: {
      id: 'mock-user-id-123',
      email: '<EMAIL>',
      role: 'student'
    }
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    statusCode: 404,
    message: 'Route not found',
    error: 'Not Found'
  });
});

// Error handler
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    statusCode: 500,
    message: 'Internal server error',
    error: 'Internal Server Error'
  });
});

const port = process.env.PORT || 3001;
app.listen(port, () => {
  console.log(`🚀 ArabLMS Mock Backend is running on: http://localhost:${port}/api`);
  console.log(`📋 Available endpoints:`);
  console.log(`   GET  /api/health`);
  console.log(`   POST /api/auth/register`);
  console.log(`   POST /api/auth/login`);
  console.log(`   POST /api/auth/refresh-token`);
  console.log(`   POST /api/auth/logout`);
  console.log(`   GET  /api/auth/profile`);
  console.log(`   GET  /api/rbac-examples/admin-only`);
  console.log(`   GET  /api/rbac-examples/authenticated`);
  console.log(`\n💡 This is a mock server for testing the frontend while we fix the NestJS build issues.`);
});
