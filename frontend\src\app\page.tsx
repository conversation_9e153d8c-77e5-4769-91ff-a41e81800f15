import { Button } from "@/components/ui/button";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center space-y-8">
          {/* Header */}
          <div className="space-y-4">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white">
              مرحباً بكم في{" "}
              <span className="text-blue-600 dark:text-blue-400">ArabLMS</span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              منصة تعليمية متطورة تمكن المدرسين من إنشاء مدارس رقمية مخصصة وتقديم الدورات للطلاب
            </p>
          </div>

          {/* Features */}
          <div className="grid md:grid-cols-3 gap-8 mt-16">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
              <div className="text-3xl mb-4">🎓</div>
              <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">للمدرسين</h3>
              <p className="text-gray-600 dark:text-gray-300">
                إنشاء مدرسة رقمية بعلامة تجارية مخصصة وتقديم الدورات بسهولة
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
              <div className="text-3xl mb-4">📚</div>
              <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">للطلاب</h3>
              <p className="text-gray-600 dark:text-gray-300">
                اكتشاف وشراء واستهلاك الدورات التعليمية بسهولة ويسر
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
              <div className="text-3xl mb-4">💰</div>
              <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">للمنصة</h3>
              <p className="text-gray-600 dark:text-gray-300">
                إيرادات مستدامة من خلال رسوم المعاملات
              </p>
            </div>
          </div>

          {/* Tech Stack */}
          <div className="mt-16 space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              التقنيات المستخدمة
            </h2>
            <div className="flex flex-wrap justify-center gap-4">
              <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm">
                Next.js 15+
              </span>
              <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm">
                NestJS
              </span>
              <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm">
                TypeScript
              </span>
              <span className="bg-cyan-100 dark:bg-cyan-900 text-cyan-800 dark:text-cyan-200 px-3 py-1 rounded-full text-sm">
                Tailwind CSS
              </span>
              <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm">
                Shadcn UI
              </span>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mt-12">
            <Button size="lg" className="text-lg px-8 py-3">
              ابدأ كمدرس
            </Button>
            <Button variant="outline" size="lg" className="text-lg px-8 py-3">
              تصفح الدورات
            </Button>
          </div>

          {/* Status */}
          <div className="mt-16 p-6 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
            <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
              🚧 حالة المشروع
            </h3>
            <p className="text-yellow-700 dark:text-yellow-300">
              المشروع قيد التطوير - تم إعداد البنية الأساسية بنجاح!
            </p>
            <div className="mt-4 text-sm text-yellow-600 dark:text-yellow-400">
              <p>✅ إعداد Next.js 15+ مع App Router</p>
              <p>✅ إعداد NestJS Backend</p>
              <p>✅ تكوين TypeScript</p>
              <p>✅ إعداد Tailwind CSS و Shadcn UI</p>
              <p>✅ دعم RTL للغة العربية</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
