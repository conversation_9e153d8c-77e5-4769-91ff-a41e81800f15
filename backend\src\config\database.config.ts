import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { entities } from '../entities';

export const getDatabaseConfig = (configService: ConfigService): TypeOrmModuleOptions => {
  const isProduction = configService.get('NODE_ENV') === 'production';

  // Supabase PostgreSQL configuration
  return {
    type: 'postgres',
    url: configService.get('DATABASE_URL'),
    host: configService.get('DATABASE_HOST', 'db.nxorbfmezftndlzplkob.supabase.co'),
    port: configService.get('DATABASE_PORT', 5432),
    username: configService.get('DATABASE_USERNAME', 'postgres'),
    password: configService.get('DATABASE_PASSWORD'),
    database: configService.get('DATABASE_NAME', 'postgres'),
    entities,
    migrations: ['dist/migrations/*.js'],
    migrationsTableName: 'typeorm_migrations',
    migrationsRun: false, // We'll run migrations manually via Supabase
    synchronize: false, // Never use synchronize - we use Supabase migrations
    logging: !isProduction ? ['error', 'warn'] : ['error'], // Reduced logging for Supabase
    ssl: true, // Supabase requires SSL
    extra: {
      // Connection pool settings optimized for Supabase
      max: 10, // Reduced for Supabase limits
      min: 2,
      acquire: 30000,
      idle: 10000,
      ssl: {
        rejectUnauthorized: false, // Required for Supabase
      },
    },
  };
};
