import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  Unique,
} from 'typeorm';
import { User } from './user.entity';
import { Course } from './course.entity';

export enum EnrollmentStatus {
  ACTIVE = 'active',
  COMPLETED = 'completed',
  SUSPENDED = 'suspended',
  CANCELLED = 'cancelled',
}

@Entity('enrollments')
@Unique(['user_id', 'course_id'])
@Index(['user_id'])
@Index(['course_id'])
@Index(['status'])
@Index(['created_at'])
export class Enrollment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: EnrollmentStatus,
    default: EnrollmentStatus.ACTIVE,
  })
  status: EnrollmentStatus;

  @Column({ type: 'jsonb', nullable: true })
  progress: {
    completedLessons?: string[]; // Array of lesson IDs
    currentLesson?: string; // Current lesson ID
    totalLessons?: number;
    completedModules?: string[]; // Array of module IDs
    totalModules?: number;
    progressPercentage?: number; // 0-100
    timeSpent?: number; // in minutes
    lastAccessedAt?: string;
    quizScores?: Array<{
      lessonId: string;
      score: number;
      maxScore: number;
      attempts: number;
      completedAt: string;
    }>;
    assignmentSubmissions?: Array<{
      lessonId: string;
      submissionUrl?: string;
      submissionText?: string;
      submittedAt: string;
      score?: number;
      feedback?: string;
      gradedAt?: string;
    }>;
  };

  @Column({ type: 'timestamp with time zone', nullable: true })
  completed_at: Date;

  @Column({ type: 'timestamp with time zone', nullable: true })
  expires_at: Date;

  @Column({ type: 'jsonb', nullable: true })
  metadata: {
    enrollmentSource?: 'direct' | 'coupon' | 'free' | 'admin';
    couponCode?: string;
    certificateIssued?: boolean;
    certificateUrl?: string;
    certificateIssuedAt?: string;
    notes?: string;
  };

  @Column({ type: 'uuid' })
  user_id: string;

  @Column({ type: 'uuid' })
  course_id: string;

  @CreateDateColumn({ type: 'timestamp with time zone' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone' })
  updated_at: Date;

  // Relations
  @ManyToOne(() => User, (user) => user.enrollments, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => Course, (course) => course.enrollments, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'course_id' })
  course: Course;
}
