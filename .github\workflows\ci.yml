name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Install frontend dependencies
      run: cd frontend && npm ci
    
    - name: Install backend dependencies
      run: cd backend && npm ci
    
    - name: Lint code
      run: npm run lint
    
    - name: Format check
      run: npm run format:check
    
    - name: Build frontend
      run: npm run build:frontend
    
    - name: Build backend
      run: npm run build:backend
    
    - name: Run frontend tests
      run: cd frontend && npm test -- --coverage --watchAll=false
    
    - name: Run backend tests
      run: cd backend && npm test -- --coverage --watchAll=false

  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Run security audit
      run: |
        npm audit --audit-level moderate
        cd frontend && npm audit --audit-level moderate
        cd ../backend && npm audit --audit-level moderate
