import {
  Injectable,
  ExecutionContext,
  Logger,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { JwtService } from '../jwt.service';

@Injectable()
export class OptionalAuthGuard extends AuthGuard('jwt') {
  private readonly logger = new Logger(OptionalAuthGuard.name);

  constructor(private readonly jwtService: JwtService) {
    super();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;

    // If no auth header, allow access but without user
    if (!authHeader) {
      this.logger.debug('No authorization header - allowing anonymous access', {
        path: request.path,
        method: request.method,
      });
      return true;
    }

    const token = this.jwtService.extractTokenFromHeader(authHeader);

    // If invalid header format, allow access but without user
    if (!token || !this.jwtService.isValidTokenFormat(token)) {
      this.logger.debug('Invalid token format - allowing anonymous access', {
        path: request.path,
        method: request.method,
      });
      return true;
    }

    try {
      // Try to validate token and attach user
      const user = await this.jwtService.validateToken(token);

      if (user) {
        request.user = user;
        request.token = token;

        this.logger.debug('User authenticated via optional guard', {
          userId: user.id,
          email: user.email,
          role: user.role,
          path: request.path,
          method: request.method,
        });

        this.jwtService.logAuthEvent('OPTIONAL_GUARD_AUTH_SUCCESS', user.id, {
          path: request.path,
          method: request.method,
        });
      } else {
        this.logger.debug('Token validation failed - allowing anonymous access', {
          path: request.path,
          method: request.method,
        });
      }

      // Always allow access regardless of token validity
      return true;
    } catch (error) {
      this.logger.warn('Optional auth guard error - allowing anonymous access', {
        error: error.message,
        path: request.path,
        method: request.method,
      });

      // Always allow access even if there's an error
      return true;
    }
  }

  handleRequest(err: any, user: any, info: any, context: ExecutionContext) {
    // Don't throw errors in optional auth - just return user or null
    return user || null;
  }
}
