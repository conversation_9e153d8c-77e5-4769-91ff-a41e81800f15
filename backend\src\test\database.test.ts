import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { DataSource, Repository } from 'typeorm';
import { getDatabaseConfig } from '../config/database.config';
import {
  User,
  School,
  Course,
  Module,
  Lesson,
  Enrollment,
  Payment,
  UserRole,
  CourseStatus,
  CourseLevel,
  LessonContentType,
  LessonStatus,
  EnrollmentStatus,
  PaymentStatus,
  PaymentMethod,
} from '../entities';

describe('Database Schema Tests', () => {
  let module: TestingModule;
  let dataSource: DataSource;
  let userRepository: Repository<User>;
  let schoolRepository: Repository<School>;
  let courseRepository: Repository<Course>;
  let moduleRepository: Repository<Module>;
  let lessonRepository: Repository<Lesson>;
  let enrollmentRepository: Repository<Enrollment>;
  let paymentRepository: Repository<Payment>;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        TypeOrmModule.forRootAsync({
          imports: [ConfigModule],
          useFactory: (configService: ConfigService) => ({
            ...getDatabaseConfig(configService),
            database: 'arablms_test',
            synchronize: true, // Only for testing
            dropSchema: true, // Clean slate for each test run
          }),
          inject: [ConfigService],
        }),
        TypeOrmModule.forFeature([
          User,
          School,
          Course,
          Module,
          Lesson,
          Enrollment,
          Payment,
        ]),
      ],
    }).compile();

    dataSource = module.get<DataSource>(DataSource);
    userRepository = module.get('UserRepository');
    schoolRepository = module.get('SchoolRepository');
    courseRepository = module.get('CourseRepository');
    moduleRepository = module.get('ModuleRepository');
    lessonRepository = module.get('LessonRepository');
    enrollmentRepository = module.get('EnrollmentRepository');
    paymentRepository = module.get('PaymentRepository');
  });

  afterAll(async () => {
    await dataSource.destroy();
    await module.close();
  });

  beforeEach(async () => {
    // Clean up data before each test
    await paymentRepository.delete({});
    await enrollmentRepository.delete({});
    await lessonRepository.delete({});
    await moduleRepository.delete({});
    await courseRepository.delete({});
    await schoolRepository.delete({});
    await userRepository.delete({});
  });

  describe('User Entity Tests', () => {
    it('should create a user with valid data', async () => {
      const userData = {
        email: '<EMAIL>',
        password_hash: 'hashed_password',
        role: UserRole.STUDENT,
        profile_data: {
          firstName: 'John',
          lastName: 'Doe',
          country: 'Egypt',
        },
      };

      const user = userRepository.create(userData);
      const savedUser = await userRepository.save(user);

      expect(savedUser.id).toBeDefined();
      expect(savedUser.email).toBe(userData.email);
      expect(savedUser.role).toBe(UserRole.STUDENT);
      expect(savedUser.profile_data.firstName).toBe('John');
      expect(savedUser.created_at).toBeDefined();
      expect(savedUser.updated_at).toBeDefined();
    });

    it('should enforce unique email constraint', async () => {
      const userData = {
        email: '<EMAIL>',
        password_hash: 'hashed_password',
        role: UserRole.STUDENT,
      };

      const user1 = userRepository.create(userData);
      await userRepository.save(user1);

      const user2 = userRepository.create(userData);
      
      await expect(userRepository.save(user2)).rejects.toThrow();
    });

    it('should validate user roles', async () => {
      const roles = [UserRole.STUDENT, UserRole.INSTRUCTOR, UserRole.ADMIN];
      
      for (const role of roles) {
        const user = userRepository.create({
          email: `${role}@example.com`,
          password_hash: 'hashed_password',
          role,
        });
        
        const savedUser = await userRepository.save(user);
        expect(savedUser.role).toBe(role);
      }
    });
  });

  describe('School Entity Tests', () => {
    let testUser: User;

    beforeEach(async () => {
      testUser = await userRepository.save({
        email: '<EMAIL>',
        password_hash: 'hashed_password',
        role: UserRole.INSTRUCTOR,
      });
    });

    it('should create a school with valid data', async () => {
      const schoolData = {
        name: 'Test School',
        subdomain: 'test-school',
        logo_url: 'https://example.com/logo.png',
        primary_color: '#FF5733',
        description: 'A test school',
        owner_id: testUser.id,
        settings: {
          currency: 'EGP',
          language: 'ar',
          allowRegistration: true,
        },
      };

      const school = schoolRepository.create(schoolData);
      const savedSchool = await schoolRepository.save(school);

      expect(savedSchool.id).toBeDefined();
      expect(savedSchool.name).toBe(schoolData.name);
      expect(savedSchool.subdomain).toBe(schoolData.subdomain);
      expect(savedSchool.settings.currency).toBe('EGP');
    });

    it('should enforce unique subdomain constraint', async () => {
      const schoolData = {
        name: 'Test School',
        subdomain: 'duplicate-subdomain',
        owner_id: testUser.id,
      };

      const school1 = schoolRepository.create(schoolData);
      await schoolRepository.save(school1);

      const school2 = schoolRepository.create({
        ...schoolData,
        name: 'Another School',
      });
      
      await expect(schoolRepository.save(school2)).rejects.toThrow();
    });

    it('should maintain foreign key relationship with user', async () => {
      const school = await schoolRepository.save({
        name: 'Test School',
        subdomain: 'test-school',
        owner_id: testUser.id,
      });

      const schoolWithOwner = await schoolRepository.findOne({
        where: { id: school.id },
        relations: ['owner'],
      });

      expect(schoolWithOwner.owner.id).toBe(testUser.id);
      expect(schoolWithOwner.owner.email).toBe(testUser.email);
    });
  });

  describe('Course Entity Tests', () => {
    let testUser: User;
    let testSchool: School;

    beforeEach(async () => {
      testUser = await userRepository.save({
        email: '<EMAIL>',
        password_hash: 'hashed_password',
        role: UserRole.INSTRUCTOR,
      });

      testSchool = await schoolRepository.save({
        name: 'Test School',
        subdomain: 'test-school',
        owner_id: testUser.id,
      });
    });

    it('should create a course with valid data', async () => {
      const courseData = {
        title: 'Test Course',
        description: 'A comprehensive test course',
        short_description: 'Test course description',
        price: 99.99,
        status: CourseStatus.PUBLISHED,
        published: true,
        level: CourseLevel.BEGINNER,
        category: 'Programming',
        tags: ['javascript', 'web development'],
        school_id: testSchool.id,
        metadata: {
          duration: 120,
          language: 'ar',
          certificateEnabled: true,
        },
      };

      const course = courseRepository.create(courseData);
      const savedCourse = await courseRepository.save(course);

      expect(savedCourse.id).toBeDefined();
      expect(savedCourse.title).toBe(courseData.title);
      expect(savedCourse.price).toBe(courseData.price);
      expect(savedCourse.tags).toEqual(courseData.tags);
      expect(savedCourse.metadata.duration).toBe(120);
    });

    it('should validate course status and level enums', async () => {
      const course = courseRepository.create({
        title: 'Test Course',
        school_id: testSchool.id,
        status: CourseStatus.DRAFT,
        level: CourseLevel.ADVANCED,
      });

      const savedCourse = await courseRepository.save(course);
      expect(savedCourse.status).toBe(CourseStatus.DRAFT);
      expect(savedCourse.level).toBe(CourseLevel.ADVANCED);
    });
  });

  describe('Module and Lesson Entity Tests', () => {
    let testCourse: Course;

    beforeEach(async () => {
      const testUser = await userRepository.save({
        email: '<EMAIL>',
        password_hash: 'hashed_password',
        role: UserRole.INSTRUCTOR,
      });

      const testSchool = await schoolRepository.save({
        name: 'Test School',
        subdomain: 'test-school',
        owner_id: testUser.id,
      });

      testCourse = await courseRepository.save({
        title: 'Test Course',
        school_id: testSchool.id,
      });
    });

    it('should create modules and lessons with proper hierarchy', async () => {
      const module = await moduleRepository.save({
        title: 'Test Module',
        description: 'A test module',
        order: 1,
        course_id: testCourse.id,
      });

      const lesson = await lessonRepository.save({
        title: 'Test Lesson',
        description: 'A test lesson',
        content_type: LessonContentType.VIDEO,
        content: {
          videoUrl: 'https://example.com/video.mp4',
          videoDuration: 600,
          videoProvider: 'bunny',
        },
        order: 1,
        status: LessonStatus.PUBLISHED,
        module_id: module.id,
      });

      expect(module.id).toBeDefined();
      expect(lesson.id).toBeDefined();
      expect(lesson.content_type).toBe(LessonContentType.VIDEO);
      expect(lesson.content.videoUrl).toBe('https://example.com/video.mp4');

      // Test relationships
      const moduleWithLessons = await moduleRepository.findOne({
        where: { id: module.id },
        relations: ['lessons'],
      });

      expect(moduleWithLessons.lessons).toHaveLength(1);
      expect(moduleWithLessons.lessons[0].id).toBe(lesson.id);
    });
  });

  describe('Enrollment and Payment Entity Tests', () => {
    let testUser: User;
    let testCourse: Course;

    beforeEach(async () => {
      testUser = await userRepository.save({
        email: '<EMAIL>',
        password_hash: 'hashed_password',
        role: UserRole.STUDENT,
      });

      const instructor = await userRepository.save({
        email: '<EMAIL>',
        password_hash: 'hashed_password',
        role: UserRole.INSTRUCTOR,
      });

      const testSchool = await schoolRepository.save({
        name: 'Test School',
        subdomain: 'test-school',
        owner_id: instructor.id,
      });

      testCourse = await courseRepository.save({
        title: 'Test Course',
        price: 99.99,
        school_id: testSchool.id,
      });
    });

    it('should create enrollment and payment records', async () => {
      const enrollment = await enrollmentRepository.save({
        status: EnrollmentStatus.ACTIVE,
        progress: {
          completedLessons: [],
          progressPercentage: 0,
          timeSpent: 0,
        },
        user_id: testUser.id,
        course_id: testCourse.id,
      });

      const payment = await paymentRepository.save({
        amount: 99.99,
        currency: 'EGP',
        platform_fee: 9.99,
        instructor_amount: 90.00,
        status: PaymentStatus.COMPLETED,
        payment_method: PaymentMethod.STRIPE,
        stripe_payment_id: 'pi_test_123',
        user_id: testUser.id,
        course_id: testCourse.id,
      });

      expect(enrollment.id).toBeDefined();
      expect(enrollment.status).toBe(EnrollmentStatus.ACTIVE);
      expect(payment.id).toBeDefined();
      expect(payment.amount).toBe(99.99);
      expect(payment.status).toBe(PaymentStatus.COMPLETED);
    });

    it('should enforce unique enrollment constraint', async () => {
      const enrollmentData = {
        user_id: testUser.id,
        course_id: testCourse.id,
        status: EnrollmentStatus.ACTIVE,
      };

      const enrollment1 = enrollmentRepository.create(enrollmentData);
      await enrollmentRepository.save(enrollment1);

      const enrollment2 = enrollmentRepository.create(enrollmentData);
      
      await expect(enrollmentRepository.save(enrollment2)).rejects.toThrow();
    });
  });

  describe('Database Constraints and Indexes', () => {
    it('should have proper indexes for performance', async () => {
      // This test would check if indexes exist in the database
      // For now, we'll just verify that queries work efficiently
      const users = await userRepository.find({
        where: { role: UserRole.INSTRUCTOR },
      });
      
      expect(Array.isArray(users)).toBe(true);
    });

    it('should cascade delete properly', async () => {
      const user = await userRepository.save({
        email: '<EMAIL>',
        password_hash: 'hashed_password',
        role: UserRole.INSTRUCTOR,
      });

      const school = await schoolRepository.save({
        name: 'Test School',
        subdomain: 'test-school',
        owner_id: user.id,
      });

      const course = await courseRepository.save({
        title: 'Test Course',
        school_id: school.id,
      });

      // Delete the school should cascade to courses
      await schoolRepository.delete(school.id);
      
      const remainingCourses = await courseRepository.find({
        where: { school_id: school.id },
      });
      
      expect(remainingCourses).toHaveLength(0);
    });
  });
});
