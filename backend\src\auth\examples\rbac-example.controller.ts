import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { RbacGuard } from '../rbac/rbac.guard';
import { CurrentUser } from '../decorators/user.decorator';
import { User } from '../../entities/user.entity';
import {
  Roles,
  Permissions,
  RequireAllPermissions,
  Resource,
  AdminOnly,
  InstructorOrAdmin,
  AuthenticatedOnly,
  SchoolManagement,
  CourseManagement,
  ManageOwnSchool,
  ManageOwnCourse,
  ReadCourses,
  EnrollmentAccess,
} from '../rbac/rbac.decorators';
import { UserRole, Permission } from '../rbac/roles.enum';

/**
 * Example controller demonstrating RBAC usage
 * This controller shows various ways to protect routes using roles and permissions
 */
@Controller('rbac-examples')
@UseGuards(JwtAuthGuard, RbacGuard)
export class RbacExampleController {

  // Example 1: Admin-only route
  @Get('admin-only')
  @AdminOnly()
  async adminOnlyRoute(@CurrentUser() user: User) {
    return {
      message: 'This route is accessible only by admins',
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
      },
    };
  }

  // Example 2: Multiple roles allowed
  @Get('instructor-or-admin')
  @InstructorOrAdmin()
  async instructorOrAdminRoute(@CurrentUser() user: User) {
    return {
      message: 'This route is accessible by instructors and admins',
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
      },
    };
  }

  // Example 3: Any authenticated user
  @Get('authenticated')
  @AuthenticatedOnly()
  async authenticatedRoute(@CurrentUser() user: User) {
    return {
      message: 'This route is accessible by any authenticated user',
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
      },
    };
  }

  // Example 4: Specific permission required
  @Get('read-courses')
  @ReadCourses()
  async readCoursesRoute(@CurrentUser() user: User) {
    return {
      message: 'This route requires READ_COURSE permission',
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
      },
    };
  }

  // Example 5: Multiple permissions (ANY)
  @Get('school-management')
  @SchoolManagement()
  async schoolManagementRoute(@CurrentUser() user: User) {
    return {
      message: 'This route requires any school management permission',
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
      },
    };
  }

  // Example 6: Multiple permissions (ALL required)
  @Post('create-and-manage-course')
  @Permissions(Permission.CREATE_COURSE, Permission.MANAGE_COURSES)
  @RequireAllPermissions()
  async createAndManageCourseRoute(@CurrentUser() user: User, @Body() courseData: any) {
    return {
      message: 'This route requires BOTH CREATE_COURSE AND MANAGE_COURSES permissions',
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
      },
      courseData,
    };
  }

  // Example 7: Resource-based access control
  @Get('schools/:schoolId')
  @Resource('school', 'schoolId', Permission.READ_SCHOOL)
  async getSchoolRoute(@CurrentUser() user: User, @Param('schoolId') schoolId: string) {
    return {
      message: 'This route checks if user can read this specific school',
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
      },
      schoolId,
    };
  }

  // Example 8: Own resource management
  @Put('schools/:schoolId')
  @ManageOwnSchool('schoolId')
  async updateSchoolRoute(
    @CurrentUser() user: User,
    @Param('schoolId') schoolId: string,
    @Body() updateData: any,
  ) {
    return {
      message: 'This route allows instructors/admins to update their own schools',
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
      },
      schoolId,
      updateData,
    };
  }

  // Example 9: Course management with ownership
  @Delete('courses/:courseId')
  @ManageOwnCourse('courseId')
  async deleteCourseRoute(@CurrentUser() user: User, @Param('courseId') courseId: string) {
    return {
      message: 'This route allows instructors/admins to delete their own courses',
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
      },
      courseId,
    };
  }

  // Example 10: Student enrollment
  @Post('enrollments')
  @EnrollmentAccess()
  async createEnrollmentRoute(@CurrentUser() user: User, @Body() enrollmentData: any) {
    return {
      message: 'This route allows users with enrollment permissions to create enrollments',
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
      },
      enrollmentData,
    };
  }

  // Example 11: Complex role and permission combination
  @Get('analytics/:schoolId')
  @Roles(UserRole.INSTRUCTOR, UserRole.ADMIN)
  @Permissions(Permission.READ_ANALYTICS)
  @Resource('school', 'schoolId')
  async getSchoolAnalyticsRoute(
    @CurrentUser() user: User,
    @Param('schoolId') schoolId: string,
  ) {
    return {
      message: 'This route requires instructor/admin role, analytics permission, and school access',
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
      },
      schoolId,
    };
  }

  // Example 12: User profile management (own profile only)
  @Put('profile')
  @Permissions(Permission.UPDATE_USER)
  async updateProfileRoute(@CurrentUser() user: User, @Body() profileData: any) {
    return {
      message: 'This route allows users to update their own profile',
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
      },
      profileData,
    };
  }

  // Example 13: System administration
  @Get('system/logs')
  @Permissions(Permission.READ_LOGS)
  async getSystemLogsRoute(@CurrentUser() user: User) {
    return {
      message: 'This route requires system log reading permission (admin only)',
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
      },
    };
  }
}
