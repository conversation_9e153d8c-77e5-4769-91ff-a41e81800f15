import { SetMetadata } from '@nestjs/common';
import { UserRole, Permission } from './roles.enum';
import { 
  ROLES_KEY, 
  PERMISSIONS_KEY, 
  REQUIRE_ALL_PERMISSIONS_KEY, 
  RESOURCE_KEY 
} from './rbac.guard';

/**
 * Decorator to specify required roles for a route
 * @param roles - Array of roles that can access the route
 */
export const Roles = (...roles: UserRole[]) => SetMetadata(ROLES_KEY, roles);

/**
 * Decorator to specify required permissions for a route
 * @param permissions - Array of permissions required to access the route
 */
export const Permissions = (...permissions: Permission[]) => 
  SetMetadata(PERMISSIONS_KEY, permissions);

/**
 * Decorator to require ALL specified permissions (instead of ANY)
 * Use in combination with @Permissions decorator
 */
export const RequireAllPermissions = () => 
  SetMetadata(REQUIRE_ALL_PERMISSIONS_KEY, true);

/**
 * Decorator to specify resource-based access control
 * @param type - Type of resource (e.g., 'school', 'course', 'lesson')
 * @param idParam - Parameter name in the route that contains the resource ID
 * @param permission - Optional specific permission required for this resource
 */
export const Resource = (
  type: string, 
  idParam: string = 'id', 
  permission?: Permission
) => SetMetadata(RESOURCE_KEY, { type, idParam, permission });

/**
 * Decorator for admin-only routes
 */
export const AdminOnly = () => Roles(UserRole.ADMIN);

/**
 * Decorator for instructor and admin routes
 */
export const InstructorOrAdmin = () => Roles(UserRole.INSTRUCTOR, UserRole.ADMIN);

/**
 * Decorator for authenticated users (any role)
 */
export const AuthenticatedOnly = () => Roles(UserRole.STUDENT, UserRole.INSTRUCTOR, UserRole.ADMIN);

/**
 * Decorator for school management permissions
 */
export const SchoolManagement = () => Permissions(
  Permission.CREATE_SCHOOL,
  Permission.READ_SCHOOL,
  Permission.UPDATE_SCHOOL,
  Permission.DELETE_SCHOOL
);

/**
 * Decorator for course management permissions
 */
export const CourseManagement = () => Permissions(
  Permission.CREATE_COURSE,
  Permission.READ_COURSE,
  Permission.UPDATE_COURSE,
  Permission.DELETE_COURSE
);

/**
 * Decorator for lesson management permissions
 */
export const LessonManagement = () => Permissions(
  Permission.CREATE_LESSON,
  Permission.READ_LESSON,
  Permission.UPDATE_LESSON,
  Permission.DELETE_LESSON
);

/**
 * Decorator for user management permissions
 */
export const UserManagement = () => Permissions(
  Permission.CREATE_USER,
  Permission.READ_USER,
  Permission.UPDATE_USER,
  Permission.DELETE_USER
);

/**
 * Decorator for payment management permissions
 */
export const PaymentManagement = () => Permissions(
  Permission.CREATE_PAYMENT,
  Permission.READ_PAYMENT,
  Permission.UPDATE_PAYMENT,
  Permission.DELETE_PAYMENT
);

/**
 * Decorator for analytics access
 */
export const AnalyticsAccess = () => Permissions(
  Permission.READ_ANALYTICS,
  Permission.READ_REPORTS
);

/**
 * Decorator for system administration
 */
export const SystemAdmin = () => Permissions(
  Permission.MANAGE_SYSTEM,
  Permission.READ_LOGS,
  Permission.MANAGE_SETTINGS
);

/**
 * Decorator for read-only access to courses
 */
export const ReadCourses = () => Permissions(Permission.READ_COURSE);

/**
 * Decorator for read-only access to lessons
 */
export const ReadLessons = () => Permissions(Permission.READ_LESSON);

/**
 * Decorator for enrollment permissions
 */
export const EnrollmentAccess = () => Permissions(
  Permission.CREATE_ENROLLMENT,
  Permission.READ_ENROLLMENT,
  Permission.UPDATE_ENROLLMENT
);

/**
 * Decorator for content management
 */
export const ContentManagement = () => Permissions(
  Permission.CREATE_CONTENT,
  Permission.READ_CONTENT,
  Permission.UPDATE_CONTENT,
  Permission.DELETE_CONTENT
);

/**
 * Decorator for own resource access (combines role check with resource ownership)
 * @param resourceType - Type of resource
 * @param idParam - Parameter name for resource ID
 */
export const OwnResource = (resourceType: string, idParam: string = 'id') => 
  Resource(resourceType, idParam);

/**
 * Decorator for school owner access
 */
export const SchoolOwner = (idParam: string = 'schoolId') => 
  Resource('school', idParam, Permission.UPDATE_SCHOOL);

/**
 * Decorator for course owner access
 */
export const CourseOwner = (idParam: string = 'courseId') => 
  Resource('course', idParam, Permission.UPDATE_COURSE);

/**
 * Decorator for lesson owner access
 */
export const LessonOwner = (idParam: string = 'lessonId') => 
  Resource('lesson', idParam, Permission.UPDATE_LESSON);

/**
 * Composite decorator for full school management with ownership
 */
export const ManageOwnSchool = (idParam: string = 'schoolId') => (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
  InstructorOrAdmin()(target, propertyKey, descriptor);
  SchoolOwner(idParam)(target, propertyKey, descriptor);
};

/**
 * Composite decorator for full course management with ownership
 */
export const ManageOwnCourse = (idParam: string = 'courseId') => (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
  InstructorOrAdmin()(target, propertyKey, descriptor);
  CourseOwner(idParam)(target, propertyKey, descriptor);
};

/**
 * Composite decorator for full lesson management with ownership
 */
export const ManageOwnLesson = (idParam: string = 'lessonId') => (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
  InstructorOrAdmin()(target, propertyKey, descriptor);
  LessonOwner(idParam)(target, propertyKey, descriptor);
};
