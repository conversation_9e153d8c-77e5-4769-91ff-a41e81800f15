export { User, UserRole } from './user.entity';
export { School } from './school.entity';
export { Course, CourseStatus, CourseLevel } from './course.entity';
export { Module } from './module.entity';
export { Lesson, LessonContentType, LessonStatus } from './lesson.entity';
export { Enrollment, EnrollmentStatus } from './enrollment.entity';
export { Payment, PaymentStatus, PaymentMethod } from './payment.entity';

// Export all entities as an array for TypeORM configuration
import { User } from './user.entity';
import { School } from './school.entity';
import { Course } from './course.entity';
import { Module } from './module.entity';
import { Lesson } from './lesson.entity';
import { Enrollment } from './enrollment.entity';
import { Payment } from './payment.entity';

export const entities = [
  User,
  School,
  Course,
  Module,
  Lesson,
  Enrollment,
  Payment,
];
