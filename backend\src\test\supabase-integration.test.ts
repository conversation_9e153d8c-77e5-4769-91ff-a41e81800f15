import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule } from '@nestjs/config';
import { SupabaseService } from '../config/supabase.config';

describe('Supabase Integration Tests', () => {
  let module: TestingModule;
  let supabaseService: SupabaseService;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
      ],
      providers: [SupabaseService],
    }).compile();

    supabaseService = module.get<SupabaseService>(SupabaseService);
  });

  afterAll(async () => {
    await module.close();
  });

  describe('Supabase Client', () => {
    it('should initialize Supabase client', () => {
      expect(supabaseService).toBeDefined();
      expect(supabaseService.client).toBeDefined();
    });

    it('should have access to database tables', async () => {
      // Test basic table access (should work with RLS policies)
      const { data, error } = await supabaseService
        .from('users')
        .select('*')
        .limit(1);

      // This might return empty data due to RLS, but should not error
      expect(error).toBeNull();
      expect(Array.isArray(data)).toBe(true);
    });
  });

  describe('Database Schema Validation', () => {
    it('should have all required tables', async () => {
      const tables = [
        'users',
        'schools', 
        'courses',
        'modules',
        'lessons',
        'enrollments',
        'payments'
      ];

      for (const table of tables) {
        const { data, error } = await supabaseService
          .from(table)
          .select('*')
          .limit(0); // Just check if table exists

        expect(error).toBeNull();
        expect(data).toBeDefined();
      }
    });

    it('should have RLS enabled on all tables', async () => {
      // This test verifies that RLS is working by checking that
      // unauthenticated requests return empty results
      const tables = [
        'users',
        'schools', 
        'courses',
        'modules',
        'lessons',
        'enrollments',
        'payments'
      ];

      for (const table of tables) {
        const { data, error } = await supabaseService
          .from(table)
          .select('*');

        // With RLS enabled and no auth, should return empty array
        expect(error).toBeNull();
        expect(Array.isArray(data)).toBe(true);
        expect(data).toHaveLength(0);
      }
    });
  });

  describe('Authentication Methods', () => {
    it('should have authentication methods available', () => {
      expect(typeof supabaseService.signUp).toBe('function');
      expect(typeof supabaseService.signIn).toBe('function');
      expect(typeof supabaseService.signOut).toBe('function');
      expect(typeof supabaseService.getUser).toBe('function');
    });
  });

  describe('Database Operations', () => {
    it('should provide database operation methods', () => {
      expect(typeof supabaseService.from).toBe('function');
      expect(typeof supabaseService.rpc).toBe('function');
    });

    it('should provide storage operations', () => {
      expect(supabaseService.storage()).toBeDefined();
    });

    it('should provide real-time channel creation', () => {
      const channel = supabaseService.channel('test-channel');
      expect(channel).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid table access gracefully', async () => {
      const { data, error } = await supabaseService
        .from('non_existent_table')
        .select('*');

      expect(error).toBeDefined();
      expect(data).toBeNull();
    });
  });

  describe('Type Safety', () => {
    it('should provide typed database operations', async () => {
      // Test that our Database types work correctly
      const { data, error } = await supabaseService
        .from('users')
        .select('id, email, role')
        .limit(1);

      expect(error).toBeNull();
      expect(Array.isArray(data)).toBe(true);
      
      if (data && data.length > 0) {
        const user = data[0];
        expect(typeof user.id).toBe('string');
        expect(typeof user.email).toBe('string');
        expect(['student', 'instructor', 'admin']).toContain(user.role);
      }
    });
  });
});
