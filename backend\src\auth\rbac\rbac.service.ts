import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../entities/user.entity';
import { 
  UserRole, 
  Permission, 
  getRolePermissions, 
  hasPermission, 
  hasAnyPermission, 
  hasAllPermissions,
  isHigherRole 
} from './roles.enum';

export interface AccessContext {
  userId: string;
  resourceId?: string;
  resourceType?: string;
  action?: string;
  metadata?: Record<string, any>;
}

@Injectable()
export class RbacService {
  private readonly logger = new Logger(RbacService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  /**
   * Check if user has a specific permission
   */
  async userHasPermission(userId: string, permission: Permission): Promise<boolean> {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });

      if (!user) {
        this.logger.warn('User not found for permission check', { userId, permission });
        return false;
      }

      const hasAccess = hasPermission(user.role, permission);
      
      this.logger.debug('Permission check result', {
        userId,
        userRole: user.role,
        permission,
        hasAccess,
      });

      return hasAccess;
    } catch (error) {
      this.logger.error('Error checking user permission', {
        error: error.message,
        userId,
        permission,
      });
      return false;
    }
  }

  /**
   * Check if user has any of the specified permissions
   */
  async userHasAnyPermission(userId: string, permissions: Permission[]): Promise<boolean> {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });

      if (!user) {
        this.logger.warn('User not found for permission check', { userId, permissions });
        return false;
      }

      const hasAccess = hasAnyPermission(user.role, permissions);
      
      this.logger.debug('Any permission check result', {
        userId,
        userRole: user.role,
        permissions,
        hasAccess,
      });

      return hasAccess;
    } catch (error) {
      this.logger.error('Error checking user permissions', {
        error: error.message,
        userId,
        permissions,
      });
      return false;
    }
  }

  /**
   * Check if user has all of the specified permissions
   */
  async userHasAllPermissions(userId: string, permissions: Permission[]): Promise<boolean> {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });

      if (!user) {
        this.logger.warn('User not found for permission check', { userId, permissions });
        return false;
      }

      const hasAccess = hasAllPermissions(user.role, permissions);
      
      this.logger.debug('All permissions check result', {
        userId,
        userRole: user.role,
        permissions,
        hasAccess,
      });

      return hasAccess;
    } catch (error) {
      this.logger.error('Error checking user permissions', {
        error: error.message,
        userId,
        permissions,
      });
      return false;
    }
  }

  /**
   * Check if user has a specific role
   */
  async userHasRole(userId: string, role: UserRole): Promise<boolean> {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });

      if (!user) {
        this.logger.warn('User not found for role check', { userId, role });
        return false;
      }

      const hasRole = user.role === role;
      
      this.logger.debug('Role check result', {
        userId,
        userRole: user.role,
        requiredRole: role,
        hasRole,
      });

      return hasRole;
    } catch (error) {
      this.logger.error('Error checking user role', {
        error: error.message,
        userId,
        role,
      });
      return false;
    }
  }

  /**
   * Check if user has any of the specified roles
   */
  async userHasAnyRole(userId: string, roles: UserRole[]): Promise<boolean> {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });

      if (!user) {
        this.logger.warn('User not found for role check', { userId, roles });
        return false;
      }

      const hasRole = roles.includes(user.role);
      
      this.logger.debug('Any role check result', {
        userId,
        userRole: user.role,
        requiredRoles: roles,
        hasRole,
      });

      return hasRole;
    } catch (error) {
      this.logger.error('Error checking user roles', {
        error: error.message,
        userId,
        roles,
      });
      return false;
    }
  }

  /**
   * Check if user has higher role than specified role
   */
  async userHasHigherRole(userId: string, role: UserRole): Promise<boolean> {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });

      if (!user) {
        this.logger.warn('User not found for role hierarchy check', { userId, role });
        return false;
      }

      const hasHigherRole = isHigherRole(user.role, role);
      
      this.logger.debug('Role hierarchy check result', {
        userId,
        userRole: user.role,
        comparedRole: role,
        hasHigherRole,
      });

      return hasHigherRole;
    } catch (error) {
      this.logger.error('Error checking role hierarchy', {
        error: error.message,
        userId,
        role,
      });
      return false;
    }
  }

  /**
   * Get all permissions for a user
   */
  async getUserPermissions(userId: string): Promise<Permission[]> {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });

      if (!user) {
        this.logger.warn('User not found for permissions retrieval', { userId });
        return [];
      }

      const permissions = getRolePermissions(user.role);
      
      this.logger.debug('Retrieved user permissions', {
        userId,
        userRole: user.role,
        permissionCount: permissions.length,
      });

      return permissions;
    } catch (error) {
      this.logger.error('Error retrieving user permissions', {
        error: error.message,
        userId,
      });
      return [];
    }
  }

  /**
   * Check resource ownership (for resource-based access control)
   */
  async checkResourceOwnership(context: AccessContext): Promise<boolean> {
    try {
      const { userId, resourceId, resourceType } = context;

      if (!resourceId || !resourceType) {
        return false;
      }

      // This is a simplified implementation
      // In a real application, you would check ownership based on the resource type
      // For example, check if user owns a school, course, etc.
      
      this.logger.debug('Resource ownership check', {
        userId,
        resourceId,
        resourceType,
      });

      // TODO: Implement actual resource ownership checks based on resource type
      // For now, return true for demonstration
      return true;
    } catch (error) {
      this.logger.error('Error checking resource ownership', {
        error: error.message,
        context,
      });
      return false;
    }
  }

  /**
   * Comprehensive access check combining role, permission, and resource ownership
   */
  async checkAccess(
    userId: string,
    permission: Permission,
    context?: AccessContext,
  ): Promise<boolean> {
    try {
      // First check if user has the required permission
      const hasRequiredPermission = await this.userHasPermission(userId, permission);
      
      if (!hasRequiredPermission) {
        this.logger.debug('Access denied: insufficient permissions', {
          userId,
          permission,
          context,
        });
        return false;
      }

      // If context is provided, check resource ownership for non-admin users
      if (context) {
        const user = await this.userRepository.findOne({
          where: { id: userId },
        });

        if (user && user.role !== UserRole.ADMIN) {
          const ownsResource = await this.checkResourceOwnership({
            ...context,
            userId,
          });

          if (!ownsResource) {
            this.logger.debug('Access denied: resource ownership check failed', {
              userId,
              permission,
              context,
            });
            return false;
          }
        }
      }

      this.logger.debug('Access granted', {
        userId,
        permission,
        context,
      });

      return true;
    } catch (error) {
      this.logger.error('Error in comprehensive access check', {
        error: error.message,
        userId,
        permission,
        context,
      });
      return false;
    }
  }
}
