import {
  Injectable,
  ExecutionContext,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

@Injectable()
export class LocalAuthGuard extends AuthGuard('local') {
  private readonly logger = new Logger(LocalAuthGuard.name);

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    this.logger.debug('Local authentication attempt', {
      email: request.body?.email,
      path: request.path,
      method: request.method,
      userAgent: request.headers['user-agent'],
      ip: request.ip,
    });

    try {
      const result = (await super.canActivate(context)) as boolean;
      
      if (result && request.user) {
        this.logger.log('Local authentication successful', {
          userId: request.user.id,
          email: request.user.email,
          role: request.user.role,
        });
      }

      return result;
    } catch (error) {
      this.logger.warn('Local authentication failed', {
        error: error.message,
        email: request.body?.email,
        path: request.path,
        method: request.method,
        ip: request.ip,
      });

      throw error;
    }
  }

  handleRequest(err: any, user: any, info: any, context: ExecutionContext) {
    const request = context.switchToHttp().getRequest();

    if (err || !user) {
      this.logger.error('Local authentication error', {
        error: err?.message,
        info: info?.message,
        email: request.body?.email,
        path: request.path,
        method: request.method,
      });

      throw err || new UnauthorizedException('Invalid credentials');
    }

    return user;
  }
}
