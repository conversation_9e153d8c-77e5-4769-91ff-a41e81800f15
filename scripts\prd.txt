# Educational Platform MVP - Product Requirements Document

## 1. Project Overview
### Vision
Build a streamlined educational platform MVP that enables instructors to create branded online schools and deliver courses to students, with a focus on the Middle Eastern market and Arabic language support.

### Core Value Propositions
- **For Instructors**: Simple school creation with custom branding and course delivery
- **For Students**: Easy course discovery, purchase, and consumption
- **For Platform**: Sustainable revenue through transaction fees

### Key Differentiators
- Arabic-first design with comprehensive RTL support
- Regional payment gateway integration (Stripe)
- Simplified, focused feature set for rapid deployment
- Mobile-responsive design optimized for regional usage patterns

## 2. User Roles
### Student
- Browse and purchase courses
- Consume video, text, and PDF content
- Track learning progress

### Instructor
- Create a branded school with subdomain
- Develop and publish courses
- Receive payments for course sales

### Administrator
- Approve instructor applications
- Process instructor payouts
- Manage platform users

## 3. Core Features
### 3.1 Authentication & User Management
- Email/password registration with email verification
- Profile management
- Role-based access control

### 3.2 School Setup & Branding
- Create and customize school with subdomain
- Upload school logo
- Set primary color scheme

### 3.3 Course Creation & Management
- Create courses with modules and lessons
- Support for video, text, and PDF content
- Course publishing workflow

### 3.4 Student Enrollment & Learning
- Course discovery and preview
- Enrollment for free/paid courses
- Progress tracking

### 3.5 Payment Processing
- Stripe integration for payments
- Platform fee collection (25%)
- Instructor payouts

### 3.6 Basic Administration
- Instructor application review
- User management
- Payout processing

## 4. Technical Requirements
### Frontend
- Next.js 14+ with App Router
- Tailwind CSS
- Shadcn UI components
- RTL support

### Backend
- NestJS with TypeScript
- RESTful APIs
- JWT authentication

### Database
- PostgreSQL via Supabase
- Data models for users, schools, courses, enrollments

### External Services
- Video hosting: Bunny.net
- File storage: Supabase Storage
- Payments: Stripe

## 5. Success Metrics
### User Growth
- 50+ instructors in first month
- 500+ student registrations in first month
- 2 courses per instructor average

### Financial
- $10,000 in first month transaction volume
- 25% platform fee ($2,500 revenue)
- < 3% payment processing fees

### Performance
- Page load < 3s (95% of pages)
- API response time < 200ms (99% of requests)
- 99.9% platform uptime

## 6. Implementation Phases
### Phase 1: Foundation (Weeks 1-3)
- Project setup
- Authentication system
- Database & base APIs

### Phase 2: Instructor Platform (Weeks 4-7)
- School management
- Course creation tools
- Content upload

### Phase 3: Student Experience (Weeks 8-11)
- Course discovery
- Payment integration
- Learning interface
- Progress tracking

### Phase 4: Admin & Operations (Weeks 12-13)
- Admin panel
- Financial operations

### Phase 5: Polish & Deploy (Week 14)
- Performance optimization
- Security audit
- Production deployment

## 7. Technical Constraints
- Single payment model (one-time purchases)
- Fixed 25% platform fee
- Basic analytics only
- No complex subscription tiers

## 8. Security Requirements
- JWT authentication
- Role-based access control
- Data encryption at rest and in transit
- Regular security audits
- OWASP Top 10 compliance

## 9. Mobile Responsiveness
- Support for all modern mobile devices
- Touch-friendly interfaces
- Optimized for mobile data usage
- Offline capabilities for content access

## 10. Localization
- Arabic as primary language
- RTL support
- Local currency (EGP) and payment methods
- Regional content guidelines compliance
