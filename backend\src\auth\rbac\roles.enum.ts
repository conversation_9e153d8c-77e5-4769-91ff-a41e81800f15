export enum UserRole {
  ADMIN = 'admin',
  INSTRUCTOR = 'instructor',
  STUDENT = 'student',
}

export enum Permission {
  // User Management
  CREATE_USER = 'create:user',
  READ_USER = 'read:user',
  UPDATE_USER = 'update:user',
  DELETE_USER = 'delete:user',
  MANAGE_USERS = 'manage:users',

  // School Management
  CREATE_SCHOOL = 'create:school',
  READ_SCHOOL = 'read:school',
  UPDATE_SCHOOL = 'update:school',
  DELETE_SCHOOL = 'delete:school',
  MANAGE_SCHOOLS = 'manage:schools',

  // Course Management
  CREATE_COURSE = 'create:course',
  READ_COURSE = 'read:course',
  UPDATE_COURSE = 'update:course',
  DELETE_COURSE = 'delete:course',
  MANAGE_COURSES = 'manage:courses',

  // Lesson Management
  CREATE_LESSON = 'create:lesson',
  READ_LESSON = 'read:lesson',
  UPDATE_LESSON = 'update:lesson',
  DELETE_LESSON = 'delete:lesson',
  MANAGE_LESSONS = 'manage:lessons',

  // Enrollment Management
  CREATE_ENROLLMENT = 'create:enrollment',
  READ_ENROLLMENT = 'read:enrollment',
  UPDATE_ENROLLMENT = 'update:enrollment',
  DELETE_ENROLLMENT = 'delete:enrollment',
  MANAGE_ENROLLMENTS = 'manage:enrollments',

  // Payment Management
  CREATE_PAYMENT = 'create:payment',
  READ_PAYMENT = 'read:payment',
  UPDATE_PAYMENT = 'update:payment',
  DELETE_PAYMENT = 'delete:payment',
  MANAGE_PAYMENTS = 'manage:payments',

  // Content Management
  CREATE_CONTENT = 'create:content',
  READ_CONTENT = 'read:content',
  UPDATE_CONTENT = 'update:content',
  DELETE_CONTENT = 'delete:content',
  MANAGE_CONTENT = 'manage:content',

  // Analytics and Reports
  READ_ANALYTICS = 'read:analytics',
  READ_REPORTS = 'read:reports',
  MANAGE_ANALYTICS = 'manage:analytics',

  // System Administration
  MANAGE_SYSTEM = 'manage:system',
  READ_LOGS = 'read:logs',
  MANAGE_SETTINGS = 'manage:settings',
}

// Role-Permission mapping
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.ADMIN]: [
    // Full system access
    Permission.MANAGE_USERS,
    Permission.MANAGE_SCHOOLS,
    Permission.MANAGE_COURSES,
    Permission.MANAGE_LESSONS,
    Permission.MANAGE_ENROLLMENTS,
    Permission.MANAGE_PAYMENTS,
    Permission.MANAGE_CONTENT,
    Permission.MANAGE_ANALYTICS,
    Permission.MANAGE_SYSTEM,
    Permission.READ_LOGS,
    Permission.MANAGE_SETTINGS,
    // Individual permissions for granular control
    Permission.CREATE_USER,
    Permission.READ_USER,
    Permission.UPDATE_USER,
    Permission.DELETE_USER,
    Permission.CREATE_SCHOOL,
    Permission.READ_SCHOOL,
    Permission.UPDATE_SCHOOL,
    Permission.DELETE_SCHOOL,
    Permission.CREATE_COURSE,
    Permission.READ_COURSE,
    Permission.UPDATE_COURSE,
    Permission.DELETE_COURSE,
    Permission.CREATE_LESSON,
    Permission.READ_LESSON,
    Permission.UPDATE_LESSON,
    Permission.DELETE_LESSON,
    Permission.CREATE_ENROLLMENT,
    Permission.READ_ENROLLMENT,
    Permission.UPDATE_ENROLLMENT,
    Permission.DELETE_ENROLLMENT,
    Permission.CREATE_PAYMENT,
    Permission.READ_PAYMENT,
    Permission.UPDATE_PAYMENT,
    Permission.DELETE_PAYMENT,
    Permission.CREATE_CONTENT,
    Permission.READ_CONTENT,
    Permission.UPDATE_CONTENT,
    Permission.DELETE_CONTENT,
    Permission.READ_ANALYTICS,
    Permission.READ_REPORTS,
  ],

  [UserRole.INSTRUCTOR]: [
    // School management (own schools)
    Permission.CREATE_SCHOOL,
    Permission.READ_SCHOOL,
    Permission.UPDATE_SCHOOL,
    Permission.DELETE_SCHOOL,
    // Course management (own courses)
    Permission.CREATE_COURSE,
    Permission.READ_COURSE,
    Permission.UPDATE_COURSE,
    Permission.DELETE_COURSE,
    // Lesson management (own lessons)
    Permission.CREATE_LESSON,
    Permission.READ_LESSON,
    Permission.UPDATE_LESSON,
    Permission.DELETE_LESSON,
    // Content management (own content)
    Permission.CREATE_CONTENT,
    Permission.READ_CONTENT,
    Permission.UPDATE_CONTENT,
    Permission.DELETE_CONTENT,
    // Enrollment management (own courses)
    Permission.READ_ENROLLMENT,
    Permission.UPDATE_ENROLLMENT,
    // Payment management (own courses)
    Permission.READ_PAYMENT,
    // Analytics (own data)
    Permission.READ_ANALYTICS,
    Permission.READ_REPORTS,
    // User management (limited)
    Permission.READ_USER,
    Permission.UPDATE_USER, // Own profile only
  ],

  [UserRole.STUDENT]: [
    // Read access to courses and lessons
    Permission.READ_COURSE,
    Permission.READ_LESSON,
    Permission.READ_CONTENT,
    Permission.READ_SCHOOL,
    // Enrollment management (own enrollments)
    Permission.CREATE_ENROLLMENT,
    Permission.READ_ENROLLMENT,
    Permission.UPDATE_ENROLLMENT,
    // Payment management (own payments)
    Permission.CREATE_PAYMENT,
    Permission.READ_PAYMENT,
    // User management (own profile)
    Permission.READ_USER,
    Permission.UPDATE_USER,
  ],
};

// Helper functions
export function getRolePermissions(role: UserRole): Permission[] {
  return ROLE_PERMISSIONS[role] || [];
}

export function hasPermission(userRole: UserRole, permission: Permission): boolean {
  const rolePermissions = getRolePermissions(userRole);
  return rolePermissions.includes(permission);
}

export function hasAnyPermission(userRole: UserRole, permissions: Permission[]): boolean {
  return permissions.some(permission => hasPermission(userRole, permission));
}

export function hasAllPermissions(userRole: UserRole, permissions: Permission[]): boolean {
  return permissions.every(permission => hasPermission(userRole, permission));
}

export function isHigherRole(role1: UserRole, role2: UserRole): boolean {
  const roleHierarchy = {
    [UserRole.ADMIN]: 3,
    [UserRole.INSTRUCTOR]: 2,
    [UserRole.STUDENT]: 1,
  };
  
  return roleHierarchy[role1] > roleHierarchy[role2];
}
