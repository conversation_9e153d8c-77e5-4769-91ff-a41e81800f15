import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface AuthConfig {
  jwtSecret: string;
  jwtExpiresIn: string;
  refreshTokenExpiresIn: string;
  bcryptRounds: number;
  maxLoginAttempts: number;
  lockoutDuration: number; // in minutes
  sessionTimeout: number; // in minutes
  enableSecurityHeaders: boolean;
  enableRequestLogging: boolean;
  corsOrigins: string[];
}

@Injectable()
export class AuthConfigService {
  constructor(private readonly configService: ConfigService) {}

  getAuthConfig(): AuthConfig {
    return {
      jwtSecret: this.configService.get<string>('JWT_SECRET') || 'your-super-secret-jwt-key-here',
      jwtExpiresIn: this.configService.get<string>('JWT_EXPIRES_IN') || '15m',
      refreshTokenExpiresIn: this.configService.get<string>('REFRESH_TOKEN_EXPIRES_IN') || '7d',
      bcryptRounds: parseInt(this.configService.get<string>('BCRYPT_ROUNDS') || '12'),
      maxLoginAttempts: parseInt(this.configService.get<string>('MAX_LOGIN_ATTEMPTS') || '5'),
      lockoutDuration: parseInt(this.configService.get<string>('LOCKOUT_DURATION') || '15'),
      sessionTimeout: parseInt(this.configService.get<string>('SESSION_TIMEOUT') || '60'),
      enableSecurityHeaders: this.configService.get<string>('ENABLE_SECURITY_HEADERS') !== 'false',
      enableRequestLogging: this.configService.get<string>('ENABLE_REQUEST_LOGGING') !== 'false',
      corsOrigins: this.getCorsOrigins(),
    };
  }



  getJwtSecret(): string {
    return this.getAuthConfig().jwtSecret;
  }

  getJwtExpiresIn(): string {
    return this.getAuthConfig().jwtExpiresIn;
  }

  getRefreshTokenExpiresIn(): string {
    return this.getAuthConfig().refreshTokenExpiresIn;
  }

  getBcryptRounds(): number {
    return this.getAuthConfig().bcryptRounds;
  }

  getMaxLoginAttempts(): number {
    return this.getAuthConfig().maxLoginAttempts;
  }

  getLockoutDuration(): number {
    return this.getAuthConfig().lockoutDuration;
  }

  getSessionTimeout(): number {
    return this.getAuthConfig().sessionTimeout;
  }

  isSecurityHeadersEnabled(): boolean {
    return this.getAuthConfig().enableSecurityHeaders;
  }

  isRequestLoggingEnabled(): boolean {
    return this.getAuthConfig().enableRequestLogging;
  }

  getCorsOrigins(): string[] {
    return this.getAuthConfig().corsOrigins;
  }
}
