import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { RbacService } from './rbac.service';
import { User, UserRole } from '../../entities/user.entity';
import { Permission } from './roles.enum';

describe('RbacService', () => {
  let service: RbacService;
  let userRepository: any;

  const mockAdminUser: User = {
    id: 'admin-123',
    email: '<EMAIL>',
    password_hash: 'hashed',
    role: UserRole.ADMIN,
    profile_data: {},
    created_at: new Date(),
    updated_at: new Date(),
    owned_schools: [],
    enrollments: [],
    payments: [],
  };

  const mockInstructorUser: User = {
    id: 'instructor-123',
    email: '<EMAIL>',
    password_hash: 'hashed',
    role: UserRole.INSTRUCTOR,
    profile_data: {},
    created_at: new Date(),
    updated_at: new Date(),
    owned_schools: [],
    enrollments: [],
    payments: [],
  };

  const mockStudentUser: User = {
    id: 'student-123',
    email: '<EMAIL>',
    password_hash: 'hashed',
    role: UserRole.STUDENT,
    profile_data: {},
    created_at: new Date(),
    updated_at: new Date(),
    owned_schools: [],
    enrollments: [],
    payments: [],
  };

  const mockUserRepository = {
    findOne: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RbacService,
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
      ],
    }).compile();

    service = module.get<RbacService>(RbacService);
    userRepository = module.get(getRepositoryToken(User));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('userHasPermission', () => {
    it('should return true for admin user with any permission', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockAdminUser);

      const result = await service.userHasPermission(
        mockAdminUser.id,
        Permission.CREATE_COURSE,
      );

      expect(result).toBe(true);
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { id: mockAdminUser.id },
      });
    });

    it('should return true for instructor with course creation permission', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockInstructorUser);

      const result = await service.userHasPermission(
        mockInstructorUser.id,
        Permission.CREATE_COURSE,
      );

      expect(result).toBe(true);
    });

    it('should return false for student with course creation permission', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockStudentUser);

      const result = await service.userHasPermission(
        mockStudentUser.id,
        Permission.CREATE_COURSE,
      );

      expect(result).toBe(false);
    });

    it('should return true for student with course reading permission', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockStudentUser);

      const result = await service.userHasPermission(
        mockStudentUser.id,
        Permission.READ_COURSE,
      );

      expect(result).toBe(true);
    });

    it('should return false for non-existent user', async () => {
      mockUserRepository.findOne.mockResolvedValue(null);

      const result = await service.userHasPermission(
        'non-existent-id',
        Permission.READ_COURSE,
      );

      expect(result).toBe(false);
    });
  });

  describe('userHasRole', () => {
    it('should return true for correct role', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockAdminUser);

      const result = await service.userHasRole(mockAdminUser.id, UserRole.ADMIN);

      expect(result).toBe(true);
    });

    it('should return false for incorrect role', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockStudentUser);

      const result = await service.userHasRole(mockStudentUser.id, UserRole.ADMIN);

      expect(result).toBe(false);
    });
  });

  describe('userHasAnyRole', () => {
    it('should return true when user has one of the required roles', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockInstructorUser);

      const result = await service.userHasAnyRole(mockInstructorUser.id, [
        UserRole.INSTRUCTOR,
        UserRole.ADMIN,
      ]);

      expect(result).toBe(true);
    });

    it('should return false when user does not have any of the required roles', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockStudentUser);

      const result = await service.userHasAnyRole(mockStudentUser.id, [
        UserRole.INSTRUCTOR,
        UserRole.ADMIN,
      ]);

      expect(result).toBe(false);
    });
  });

  describe('userHasAnyPermission', () => {
    it('should return true when user has at least one permission', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockInstructorUser);

      const result = await service.userHasAnyPermission(mockInstructorUser.id, [
        Permission.CREATE_COURSE,
        Permission.MANAGE_SYSTEM, // Instructor doesn't have this
      ]);

      expect(result).toBe(true);
    });

    it('should return false when user has none of the permissions', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockStudentUser);

      const result = await service.userHasAnyPermission(mockStudentUser.id, [
        Permission.CREATE_COURSE,
        Permission.MANAGE_SYSTEM,
      ]);

      expect(result).toBe(false);
    });
  });

  describe('userHasAllPermissions', () => {
    it('should return true when user has all permissions', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockAdminUser);

      const result = await service.userHasAllPermissions(mockAdminUser.id, [
        Permission.CREATE_COURSE,
        Permission.READ_COURSE,
        Permission.UPDATE_COURSE,
      ]);

      expect(result).toBe(true);
    });

    it('should return false when user is missing some permissions', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockStudentUser);

      const result = await service.userHasAllPermissions(mockStudentUser.id, [
        Permission.READ_COURSE, // Student has this
        Permission.CREATE_COURSE, // Student doesn't have this
      ]);

      expect(result).toBe(false);
    });
  });

  describe('userHasHigherRole', () => {
    it('should return true for admin compared to instructor', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockAdminUser);

      const result = await service.userHasHigherRole(
        mockAdminUser.id,
        UserRole.INSTRUCTOR,
      );

      expect(result).toBe(true);
    });

    it('should return true for instructor compared to student', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockInstructorUser);

      const result = await service.userHasHigherRole(
        mockInstructorUser.id,
        UserRole.STUDENT,
      );

      expect(result).toBe(true);
    });

    it('should return false for student compared to instructor', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockStudentUser);

      const result = await service.userHasHigherRole(
        mockStudentUser.id,
        UserRole.INSTRUCTOR,
      );

      expect(result).toBe(false);
    });
  });

  describe('getUserPermissions', () => {
    it('should return all permissions for admin user', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockAdminUser);

      const result = await service.getUserPermissions(mockAdminUser.id);

      expect(result).toContain(Permission.MANAGE_SYSTEM);
      expect(result).toContain(Permission.CREATE_COURSE);
      expect(result).toContain(Permission.READ_COURSE);
      expect(result.length).toBeGreaterThan(20); // Admin has many permissions
    });

    it('should return limited permissions for student user', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockStudentUser);

      const result = await service.getUserPermissions(mockStudentUser.id);

      expect(result).toContain(Permission.READ_COURSE);
      expect(result).toContain(Permission.CREATE_ENROLLMENT);
      expect(result).not.toContain(Permission.CREATE_COURSE);
      expect(result).not.toContain(Permission.MANAGE_SYSTEM);
    });

    it('should return empty array for non-existent user', async () => {
      mockUserRepository.findOne.mockResolvedValue(null);

      const result = await service.getUserPermissions('non-existent-id');

      expect(result).toEqual([]);
    });
  });

  describe('checkAccess', () => {
    it('should grant access for admin with any permission', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockAdminUser);

      const result = await service.checkAccess(
        mockAdminUser.id,
        Permission.CREATE_COURSE,
      );

      expect(result).toBe(true);
    });

    it('should deny access for user without permission', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockStudentUser);

      const result = await service.checkAccess(
        mockStudentUser.id,
        Permission.CREATE_COURSE,
      );

      expect(result).toBe(false);
    });
  });
});
