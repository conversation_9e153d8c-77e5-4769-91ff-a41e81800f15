import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  UnauthorizedException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';

@Catch(UnauthorizedException, ForbiddenException)
export class AuthExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(AuthExceptionFilter.name);

  catch(exception: UnauthorizedException | ForbiddenException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus();

    // Log authentication/authorization failures
    this.logger.warn('Authentication/Authorization failed', {
      statusCode: status,
      message: exception.message,
      path: request.url,
      method: request.method,
      userAgent: request.headers['user-agent'],
      ip: request.ip,
      timestamp: new Date().toISOString(),
    });

    // Determine error type and message
    const isUnauthorized = exception instanceof UnauthorizedException;
    const errorType = isUnauthorized ? 'UNAUTHORIZED' : 'FORBIDDEN';
    
    // Create standardized error response
    const errorResponse = {
      statusCode: status,
      error: errorType,
      message: this.getSecureErrorMessage(exception.message, isUnauthorized),
      timestamp: new Date().toISOString(),
      path: request.url,
    };

    // Add security headers
    response.setHeader('WWW-Authenticate', 'Bearer');
    response.setHeader('X-Content-Type-Options', 'nosniff');
    response.setHeader('X-Frame-Options', 'DENY');

    // Send response
    response.status(status).json(errorResponse);
  }

  private getSecureErrorMessage(originalMessage: string, isUnauthorized: boolean): string {
    // Return generic messages to avoid information leakage
    if (isUnauthorized) {
      // Check for specific error types
      if (originalMessage.includes('token') || originalMessage.includes('Token')) {
        return 'Invalid or expired authentication token';
      }
      if (originalMessage.includes('header') || originalMessage.includes('Header')) {
        return 'Missing or invalid authorization header';
      }
      if (originalMessage.includes('credentials') || originalMessage.includes('password')) {
        return 'Invalid credentials provided';
      }
      return 'Authentication required';
    } else {
      return 'Insufficient permissions to access this resource';
    }
  }
}
