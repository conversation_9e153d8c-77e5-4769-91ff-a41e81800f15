import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitialSchema1703000000000 implements MigrationInterface {
  name = 'InitialSchema1703000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create ENUM types
    await queryRunner.query(`
      CREATE TYPE "user_role_enum" AS ENUM('student', 'instructor', 'admin')
    `);
    
    await queryRunner.query(`
      CREATE TYPE "course_status_enum" AS ENUM('draft', 'published', 'archived')
    `);
    
    await queryRunner.query(`
      CREATE TYPE "course_level_enum" AS ENUM('beginner', 'intermediate', 'advanced')
    `);
    
    await queryRunner.query(`
      CREATE TYPE "lesson_content_type_enum" AS ENUM('video', 'text', 'quiz', 'assignment', 'document', 'live_session')
    `);
    
    await queryRunner.query(`
      CREATE TYPE "lesson_status_enum" AS ENUM('draft', 'published', 'archived')
    `);
    
    await queryRunner.query(`
      CREATE TYPE "enrollment_status_enum" AS ENUM('active', 'completed', 'suspended', 'cancelled')
    `);
    
    await queryRunner.query(`
      CREATE TYPE "payment_status_enum" AS ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded', 'partially_refunded')
    `);
    
    await queryRunner.query(`
      CREATE TYPE "payment_method_enum" AS ENUM('stripe', 'paypal', 'bank_transfer', 'wallet', 'free')
    `);

    // Create users table
    await queryRunner.query(`
      CREATE TABLE "users" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(),
        "email" character varying(255) NOT NULL,
        "password_hash" character varying(255) NOT NULL,
        "role" "user_role_enum" NOT NULL DEFAULT 'student',
        "profile_data" jsonb,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        CONSTRAINT "PK_users_id" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_users_email" UNIQUE ("email")
      )
    `);

    // Create schools table
    await queryRunner.query(`
      CREATE TABLE "schools" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(),
        "name" character varying(255) NOT NULL,
        "subdomain" character varying(100) NOT NULL,
        "logo_url" character varying(500),
        "primary_color" character varying(7) NOT NULL DEFAULT '#3B82F6',
        "description" text,
        "settings" jsonb,
        "owner_id" uuid NOT NULL,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        CONSTRAINT "PK_schools_id" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_schools_subdomain" UNIQUE ("subdomain")
      )
    `);

    // Create courses table
    await queryRunner.query(`
      CREATE TABLE "courses" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(),
        "title" character varying(255) NOT NULL,
        "description" text,
        "short_description" text,
        "price" numeric(10,2) NOT NULL DEFAULT '0',
        "thumbnail_url" character varying(500),
        "preview_video_url" character varying(500),
        "status" "course_status_enum" NOT NULL DEFAULT 'draft',
        "published" boolean NOT NULL DEFAULT false,
        "level" "course_level_enum" NOT NULL DEFAULT 'beginner',
        "category" character varying(255),
        "tags" text array NOT NULL DEFAULT '{}',
        "metadata" jsonb,
        "school_id" uuid NOT NULL,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        CONSTRAINT "PK_courses_id" PRIMARY KEY ("id")
      )
    `);

    // Create modules table
    await queryRunner.query(`
      CREATE TABLE "modules" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(),
        "title" character varying(255) NOT NULL,
        "description" text,
        "order" integer NOT NULL DEFAULT '0',
        "is_published" boolean NOT NULL DEFAULT true,
        "metadata" jsonb,
        "course_id" uuid NOT NULL,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        CONSTRAINT "PK_modules_id" PRIMARY KEY ("id")
      )
    `);

    // Create lessons table
    await queryRunner.query(`
      CREATE TABLE "lessons" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(),
        "title" character varying(255) NOT NULL,
        "description" text,
        "content_type" "lesson_content_type_enum" NOT NULL DEFAULT 'video',
        "content" jsonb,
        "order" integer NOT NULL DEFAULT '0',
        "status" "lesson_status_enum" NOT NULL DEFAULT 'draft',
        "is_published" boolean NOT NULL DEFAULT true,
        "is_free" boolean NOT NULL DEFAULT false,
        "duration" integer,
        "metadata" jsonb,
        "module_id" uuid NOT NULL,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        CONSTRAINT "PK_lessons_id" PRIMARY KEY ("id")
      )
    `);

    // Create enrollments table
    await queryRunner.query(`
      CREATE TABLE "enrollments" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(),
        "status" "enrollment_status_enum" NOT NULL DEFAULT 'active',
        "progress" jsonb,
        "completed_at" TIMESTAMP WITH TIME ZONE,
        "expires_at" TIMESTAMP WITH TIME ZONE,
        "metadata" jsonb,
        "user_id" uuid NOT NULL,
        "course_id" uuid NOT NULL,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        CONSTRAINT "PK_enrollments_id" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_enrollments_user_course" UNIQUE ("user_id", "course_id")
      )
    `);

    // Create payments table
    await queryRunner.query(`
      CREATE TABLE "payments" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(),
        "amount" numeric(10,2) NOT NULL,
        "currency" character varying(3) NOT NULL DEFAULT 'EGP',
        "platform_fee" numeric(10,2) NOT NULL DEFAULT '0',
        "instructor_amount" numeric(10,2) NOT NULL DEFAULT '0',
        "status" "payment_status_enum" NOT NULL DEFAULT 'pending',
        "payment_method" "payment_method_enum" NOT NULL DEFAULT 'stripe',
        "stripe_payment_id" character varying(255),
        "stripe_payment_intent_id" character varying(255),
        "external_transaction_id" character varying(255),
        "payment_details" jsonb,
        "paid_at" TIMESTAMP WITH TIME ZONE,
        "refunded_at" TIMESTAMP WITH TIME ZONE,
        "notes" text,
        "user_id" uuid NOT NULL,
        "course_id" uuid NOT NULL,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        CONSTRAINT "PK_payments_id" PRIMARY KEY ("id")
      )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop tables in reverse order
    await queryRunner.query(`DROP TABLE "payments"`);
    await queryRunner.query(`DROP TABLE "enrollments"`);
    await queryRunner.query(`DROP TABLE "lessons"`);
    await queryRunner.query(`DROP TABLE "modules"`);
    await queryRunner.query(`DROP TABLE "courses"`);
    await queryRunner.query(`DROP TABLE "schools"`);
    await queryRunner.query(`DROP TABLE "users"`);

    // Drop ENUM types
    await queryRunner.query(`DROP TYPE "payment_method_enum"`);
    await queryRunner.query(`DROP TYPE "payment_status_enum"`);
    await queryRunner.query(`DROP TYPE "enrollment_status_enum"`);
    await queryRunner.query(`DROP TYPE "lesson_status_enum"`);
    await queryRunner.query(`DROP TYPE "lesson_content_type_enum"`);
    await queryRunner.query(`DROP TYPE "course_level_enum"`);
    await queryRunner.query(`DROP TYPE "course_status_enum"`);
    await queryRunner.query(`DROP TYPE "user_role_enum"`);
  }
}
