import {
  Injectable,
  NestMiddleware,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { JwtService } from '../jwt.service';
import { User } from '../../entities/user.entity';

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: User;
      token?: string;
    }
  }
}

@Injectable()
export class AuthMiddleware implements NestMiddleware {
  private readonly logger = new Logger(AuthMiddleware.name);

  constructor(private readonly jwtService: JwtService) {}

  async use(req: Request, res: Response, next: NextFunction) {
    try {
      // Extract token from Authorization header
      const authHeader = req.headers.authorization;
      
      if (!authHeader) {
        // No token provided - continue without authentication
        // This allows public routes to work
        return next();
      }

      const token = this.jwtService.extractTokenFromHeader(authHeader);

      if (!token) {
        this.logger.warn('Invalid authorization header format', {
          path: req.path,
          method: req.method,
        });
        // Continue without authentication for flexibility
        return next();
      }

      if (!this.jwtService.isValidTokenFormat(token)) {
        this.logger.warn('Invalid token format', {
          path: req.path,
          method: req.method,
        });
        // Continue without authentication
        return next();
      }

      // Validate token and get user
      const user = await this.jwtService.validateToken(token);

      if (user) {
        // Attach user and token to request
        req.user = user;
        req.token = token;

        this.logger.debug('User authenticated via middleware', {
          userId: user.id,
          email: user.email,
          role: user.role,
          path: req.path,
          method: req.method,
        });

        // Log authentication event
        this.jwtService.logAuthEvent('MIDDLEWARE_AUTH_SUCCESS', user.id, {
          path: req.path,
          method: req.method,
          userAgent: req.headers['user-agent'],
          ip: req.ip,
        });
      } else {
        this.logger.warn('Token validation failed in middleware', {
          path: req.path,
          method: req.method,
        });
      }

      next();
    } catch (error) {
      this.logger.error('Authentication middleware error', {
        error: error.message,
        path: req.path,
        method: req.method,
      });

      // Don't throw error in middleware - let guards handle authentication
      // This allows for graceful degradation
      next();
    }
  }
}

@Injectable()
export class StrictAuthMiddleware implements NestMiddleware {
  private readonly logger = new Logger(StrictAuthMiddleware.name);

  constructor(private readonly jwtService: JwtService) {}

  async use(req: Request, res: Response, next: NextFunction) {
    try {
      const authHeader = req.headers.authorization;

      if (!authHeader) {
        throw new UnauthorizedException('Authorization header is required');
      }

      const token = this.jwtService.extractTokenFromHeader(authHeader);

      if (!token) {
        throw new UnauthorizedException('Invalid authorization header format');
      }

      if (!this.jwtService.isValidTokenFormat(token)) {
        throw new UnauthorizedException('Invalid token format');
      }

      const user = await this.jwtService.validateToken(token);

      if (!user) {
        throw new UnauthorizedException('Invalid or expired token');
      }

      req.user = user;
      req.token = token;

      this.logger.debug('User authenticated via strict middleware', {
        userId: user.id,
        email: user.email,
        role: user.role,
        path: req.path,
        method: req.method,
      });

      this.jwtService.logAuthEvent('STRICT_MIDDLEWARE_AUTH_SUCCESS', user.id, {
        path: req.path,
        method: req.method,
        userAgent: req.headers['user-agent'],
        ip: req.ip,
      });

      next();
    } catch (error) {
      this.logger.error('Strict authentication middleware failed', {
        error: error.message,
        path: req.path,
        method: req.method,
      });

      if (error instanceof UnauthorizedException) {
        throw error;
      }

      throw new UnauthorizedException('Authentication failed');
    }
  }
}
